#!/bin/bash

# Install Advanced GPU Management Tools for Linux
echo "=== Installing Advanced GPU Management Tools ==="
echo ""

# Function to check if package manager is available
check_package_manager() {
    if command -v pacman &> /dev/null; then
        echo "✅ Pacman detected (Arch-based system)"
        return 0
    elif command -v apt &> /dev/null; then
        echo "✅ APT detected (Debian-based system)"
        return 1
    elif command -v dnf &> /dev/null; then
        echo "✅ DNF detected (Fedora-based system)"
        return 2
    else
        echo "❌ No supported package manager found"
        return 3
    fi
}

# Check package manager
check_package_manager
PM_TYPE=$?

echo ""
echo "Installing GPU management tools..."

case $PM_TYPE in
    0) # Arch/Manjaro
        echo "Installing via pacman and AUR..."
        
        # Official repo packages
        sudo pacman -S --needed corectrl radeontop mesa-utils vulkan-tools
        
        # AUR packages (if yay is available)
        if command -v yay &> /dev/null; then
            yay -S --needed amdgpu-utils wattman-gtk radeon-profile
        elif command -v paru &> /dev/null; then
            paru -S --needed amdgpu-utils wattman-gtk radeon-profile
        else
            echo "⚠️  AUR helper not found. Install yay or paru for additional tools:"
            echo "   git clone https://aur.archlinux.org/yay.git && cd yay && makepkg -si"
        fi
        ;;
        
    1) # Ubuntu/Debian
        echo "Installing via APT..."
        sudo apt update
        sudo apt install -y radeontop mesa-utils vulkan-tools
        
        # CoreCtrl (may need PPA)
        if ! command -v corectrl &> /dev/null; then
            echo "Installing CoreCtrl..."
            sudo apt install -y corectrl || {
                echo "Adding CoreCtrl PPA..."
                sudo add-apt-repository ppa:ernstp/mesarc
                sudo apt update
                sudo apt install -y corectrl
            }
        fi
        ;;
        
    2) # Fedora
        echo "Installing via DNF..."
        sudo dnf install -y radeontop mesa-demos vulkan-tools corectrl
        ;;
        
    3) # Unknown
        echo "❌ Please install manually:"
        echo "- CoreCtrl: GUI GPU management"
        echo "- RadeonTop: GPU monitoring"
        echo "- WattmanGTK: Advanced GPU control"
        exit 1
        ;;
esac

echo ""
echo "=== Tool Overview ==="
echo ""

echo "1. **CoreCtrl** - Comprehensive GPU Management"
echo "   - GUI interface for GPU control"
echo "   - Memory and core clock adjustment"
echo "   - Fan curve management"
echo "   - Power limit control"
echo "   - Launch: corectrl"
echo ""

echo "2. **RadeonTop** - Real-time GPU Monitoring"
echo "   - Live GPU usage statistics"
echo "   - Memory usage tracking"
echo "   - Temperature monitoring"
echo "   - Launch: radeontop"
echo ""

echo "3. **WattmanGTK** - Advanced Overclocking (if installed)"
echo "   - Detailed voltage/frequency curves"
echo "   - Memory timing adjustments"
echo "   - Advanced power management"
echo "   - Launch: wattman-gtk"
echo ""

echo "4. **AMDGPU-Utils** - Command-line Tools (if installed)"
echo "   - amdgpu-monitor: GUI monitoring"
echo "   - amdgpu-plot: Performance plotting"
echo "   - amdgpu-ls: GPU information"
echo ""

echo "=== Configuration Steps ==="
echo ""

echo "1. **Enable CoreCtrl at startup:**"
echo "   - Run: corectrl"
echo "   - Go to Preferences → General"
echo "   - Enable 'Start minimized on system tray'"
echo "   - Enable 'Start at login'"
echo ""

echo "2. **Grant permissions for GPU control:**"
echo "   sudo usermod -a -G video \$USER"
echo "   # Logout and login again"
echo ""

echo "3. **Create udev rules for CoreCtrl:**"
cat << 'EOF' | sudo tee /etc/udev/rules.d/90-corectrl.rules > /dev/null
KERNEL=="card0", SUBSYSTEM=="drm", DRIVERS=="amdgpu", TAG+="uaccess"
EOF

echo "   sudo udevadm control --reload-rules"
echo "   sudo udevadm trigger"
echo ""

echo "=== Testing Tools ==="
echo ""

# Test installed tools
echo "Testing installed tools..."

if command -v corectrl &> /dev/null; then
    echo "✅ CoreCtrl installed"
    echo "   Launch with: corectrl"
    echo "   Features: GPU overclocking, fan control, monitoring"
else
    echo "❌ CoreCtrl not available"
fi

if command -v radeontop &> /dev/null; then
    echo "✅ RadeonTop installed"
    echo "   Launch with: radeontop"
    echo "   Features: Real-time GPU monitoring"
else
    echo "❌ RadeonTop not available"
fi

if command -v wattman-gtk &> /dev/null; then
    echo "✅ WattmanGTK installed"
    echo "   Launch with: wattman-gtk"
    echo "   Features: Advanced GPU control"
else
    echo "❌ WattmanGTK not available (AUR package)"
fi

if command -v amdgpu-monitor &> /dev/null; then
    echo "✅ AMDGPU-Utils installed"
    echo "   Launch with: amdgpu-monitor --gui"
    echo "   Features: Comprehensive GPU management"
else
    echo "❌ AMDGPU-Utils not available (AUR package)"
fi

echo ""
echo "=== Next Steps ==="
echo ""

echo "1. **Logout and login** to apply group permissions"
echo ""

echo "2. **Launch CoreCtrl** and try to:"
echo "   - Increase memory frequency manually"
echo "   - Set custom fan curves"
echo "   - Monitor temperatures and usage"
echo ""

echo "3. **Check if any tool can bypass VBIOS limits:**"
echo "   - Some tools may have advanced features"
echo "   - Try different overclocking profiles"
echo "   - Look for 'unlock' or 'advanced' options"
echo ""

echo "4. **Monitor with RadeonTop while testing:**"
echo "   radeontop"
echo ""

echo "⚠️  **Important Note:**"
echo "Even with these tools, your mining VBIOS may still limit"
echo "maximum achievable memory frequencies. These tools provide"
echo "better interfaces and monitoring, but cannot override"
echo "fundamental VBIOS restrictions."
echo ""

echo "For complete solution, VBIOS modification remains necessary."
