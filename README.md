# Intelligent Case Fan Control System

An intelligent fan control system for ASRock B660M-ITX/ac motherboards that automatically adjusts chassis fan speeds based on the highest temperature between CPU and GPU components.

## 🎯 Features

- **Dual-sensor monitoring**: Tracks both CPU (coretemp) and GPU (amdgpu) temperatures
- **Intelligent control**: Uses the hottest component to determine fan speeds
- **Scenario-based operation**: 
  - Gaming: Responds to GPU temperature spikes
  - CPU-intensive tasks: Responds to CPU temperature increases  
  - Idle: Maintains quiet operation when both components are cool
- **Conservative fan curves**: Prioritizes quiet operation while ensuring adequate cooling
- **Hysteresis control**: Prevents rapid fan speed oscillations
- **Automatic restoration**: Returns to BIOS fan control on service shutdown
- **Comprehensive logging**: Detailed operation logs for monitoring and debugging

## 🔧 Hardware Requirements

- **Motherboard**: ASRock B660M-ITX/ac with NCT6798 sensor chip
- **CPU**: Intel 12th gen with coretemp sensor support
- **GPU**: AMD GPU with amdgpu driver (optional but recommended)
- **Fans**: Chassis fans connected to motherboard headers (fan2-fan5)

## 📦 Installation

### Quick Setup

1. **Clone or download the scripts**:
   ```bash
   # If you have the files, make them executable
   chmod +x setup_intelligent_fan_control.sh
   ```

2. **Run the setup script**:
   ```bash
   sudo ./setup_intelligent_fan_control.sh
   ```

3. **Follow the prompts** to configure sensors and enable the service

### Manual Installation

1. **Install dependencies**:
   ```bash
   sudo pacman -S lm_sensors python3
   ```

2. **Configure sensors**:
   ```bash
   sudo sensors-detect
   # Press Enter at each prompt to accept defaults
   ```

3. **Copy files**:
   ```bash
   sudo cp intelligent_fan_control.py /usr/local/bin/
   sudo chmod +x /usr/local/bin/intelligent_fan_control.py
   sudo cp intelligent-fan-control.service /etc/systemd/system/
   sudo systemctl daemon-reload
   ```

4. **Enable and start service**:
   ```bash
   sudo systemctl enable --now intelligent-fan-control.service
   ```

## 🚀 Usage

### Service Management

```bash
# Start the service
sudo systemctl start intelligent-fan-control

# Stop the service  
sudo systemctl stop intelligent-fan-control

# Check service status
sudo systemctl status intelligent-fan-control

# Enable automatic startup
sudo systemctl enable intelligent-fan-control

# View live logs
sudo journalctl -u intelligent-fan-control -f
```

### Manual Operation

```bash
# Run manually with 5-second intervals
sudo python3 /usr/local/bin/intelligent_fan_control.py 5

# Run with custom interval
sudo python3 /usr/local/bin/intelligent_fan_control.py 10
```

### Real-time Monitoring

```bash
# Launch the monitoring dashboard
./fan_control_monitor.sh
```

The monitor provides:
- Real-time temperature readings
- Fan speed and PWM values
- Service status
- Recent log entries
- Interactive controls

## ⚙️ Configuration

### Fan Curve Customization

Edit `/usr/local/bin/intelligent_fan_control.py` and modify the `FanCurve` settings:

```python
self.fan_curve = FanCurve(
    temp_points=[30, 40, 50, 60, 70, 80, 90],    # Temperature thresholds (°C)
    speed_points=[25, 40, 70, 110, 160, 210, 255], # PWM values (0-255)
    hysteresis=3                                   # Temperature drop for speed reduction
)
```

### Example Fan Curves

**Silent Profile** (default):
```python
temp_points=[30, 40, 50, 60, 70, 80, 90]
speed_points=[25, 40, 70, 110, 160, 210, 255]
```

**Balanced Profile**:
```python
temp_points=[25, 35, 45, 55, 65, 75, 85]
speed_points=[40, 60, 90, 130, 170, 220, 255]
```

**Performance Profile**:
```python
temp_points=[20, 30, 40, 50, 60, 70, 80]
speed_points=[60, 80, 110, 150, 190, 230, 255]
```

## 📊 How It Works

1. **Temperature Monitoring**: Reads CPU package temperature and GPU edge temperature every 5 seconds
2. **Maximum Selection**: Determines which component is hottest
3. **Fan Speed Calculation**: Uses the fan curve to determine appropriate PWM value
4. **Hysteresis Application**: Prevents rapid speed changes by requiring temperature drops
5. **Fan Control**: Sets PWM values for all chassis fans (fan2-fan5)
6. **Logging**: Records temperature, component, and fan speed decisions

### Temperature Sources

- **CPU**: `/sys/class/hwmon/hwmonX/temp1_input` (coretemp sensor)
- **GPU**: `/sys/class/hwmon/hwmonY/temp1_input` (amdgpu sensor)

### Fan Control Targets

- **Chassis Fan 1**: `/sys/class/hwmon/hwmonZ/pwm2`
- **Chassis Fan 2**: `/sys/class/hwmon/hwmonZ/pwm3`  
- **Chassis Fan 3**: `/sys/class/hwmon/hwmonZ/pwm4`
- **Chassis Fan 4**: `/sys/class/hwmon/hwmonZ/pwm5`

## 🔍 Monitoring and Troubleshooting

### Check System Status

```bash
# View current temperatures and fan speeds
sensors

# Check which fans are controllable
ls /sys/class/hwmon/hwmon*/pwm* | grep -E "pwm[2-5]$"

# Monitor fan control in real-time
watch -n 1 'sensors | grep -E "(fan|temp)"'
```

### Common Issues

**Service won't start**:
- Check if running as root: `sudo systemctl status intelligent-fan-control`
- Verify sensor availability: `sensors`
- Check hardware paths: `ls /sys/class/hwmon/*/name`

**No fan control**:
- Ensure chassis fans are connected to motherboard headers
- Check PWM files exist: `ls /sys/class/hwmon/*/pwm[2-5]`
- Verify NCT6798 sensor: `sensors | grep nct6798`

**Temperature readings missing**:
- Run sensors-detect: `sudo sensors-detect`
- Check CPU sensor: `sensors | grep coretemp`
- Check GPU sensor: `sensors | grep amdgpu`

### Log Analysis

```bash
# View recent logs
sudo journalctl -u intelligent-fan-control -n 50

# Follow logs in real-time
sudo journalctl -u intelligent-fan-control -f

# Check log file
sudo tail -f /var/log/intelligent-fan-control.log
```

## 🛡️ Safety Features

- **Automatic restoration**: BIOS fan control restored on service shutdown
- **Conservative defaults**: Fan curves prioritize cooling over silence
- **Error handling**: Graceful fallback on sensor read failures
- **Signal handling**: Proper cleanup on SIGTERM/SIGINT
- **Permission checks**: Requires root access for hardware control

## 🔄 Comparison with BIOS Control

| Feature | BIOS Control | Intelligent Control |
|---------|-------------|-------------------|
| Temperature source | Single sensor | CPU + GPU maximum |
| Gaming performance | Compromise setting | GPU-responsive |
| CPU workloads | Compromise setting | CPU-responsive |
| Idle operation | Fixed curve | Adaptive minimum |
| Customization | Limited options | Fully configurable |
| Monitoring | Basic | Comprehensive logging |

## 📝 Files Overview

- `intelligent_fan_control.py` - Main fan control daemon
- `intelligent-fan-control.service` - Systemd service definition
- `setup_intelligent_fan_control.sh` - Automated setup script
- `fan_control_monitor.sh` - Real-time monitoring dashboard
- `README.md` - This documentation

## 🤝 Contributing

Feel free to submit issues, feature requests, or improvements. The system is designed to be modular and extensible.

## ⚠️ Disclaimer

This software controls hardware fans. While safety measures are implemented, use at your own risk. Monitor temperatures during initial setup to ensure proper cooling.
