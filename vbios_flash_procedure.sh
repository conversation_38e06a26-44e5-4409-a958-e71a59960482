#!/bin/bash

# VBIOS Flashing Procedure for RX 6800 XT
# CRITICAL: This modifies GPU firmware - ensure you have recovery plan

set -e  # Exit on any error

VBIOS_FILE="Sapphire.RX6800XT.16384.210112.rom"
BACKUP_FILE="original_vbios_backup_$(date +%Y%m%d_%H%M%S).rom"

echo "=== AMD RX 6800 XT VBIOS Flashing Procedure ==="
echo "⚠️  WARNING: This will modify your GPU firmware!"
echo "⚠️  Ensure you have a recovery plan if something goes wrong!"
echo ""

# Pre-flight checks
echo "1. Pre-flight Safety Checks..."

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "❌ This script must be run as root (sudo)"
    exit 1
fi

# Check if amdvbflash is available
if ! command -v amdvbflash &> /dev/null; then
    echo "❌ amdvbflash not found. Please install it first."
    exit 1
fi

# Check if VBIOS file exists
if [ ! -f "$VBIOS_FILE" ]; then
    echo "❌ VBIOS file '$VBIOS_FILE' not found in current directory"
    echo "Please ensure the file is in $(pwd)"
    exit 1
fi

# Check VBIOS file size (should be around 256KB-1MB)
VBIOS_SIZE=$(stat -c%s "$VBIOS_FILE")
if [ "$VBIOS_SIZE" -lt 200000 ] || [ "$VBIOS_SIZE" -gt 2000000 ]; then
    echo "❌ VBIOS file size ($VBIOS_SIZE bytes) seems incorrect"
    echo "Expected size: 200KB-2MB"
    exit 1
fi

echo "✅ amdvbflash found"
echo "✅ VBIOS file found: $VBIOS_FILE ($VBIOS_SIZE bytes)"

# Check GPU detection
echo ""
echo "2. GPU Detection..."
amdvbflash -i
echo ""

# Backup current VBIOS
echo "3. Backing up current VBIOS..."
echo "Creating backup: $BACKUP_FILE"
amdvbflash -s 0 "$BACKUP_FILE"

if [ ! -f "$BACKUP_FILE" ]; then
    echo "❌ Failed to create backup!"
    exit 1
fi

BACKUP_SIZE=$(stat -c%s "$BACKUP_FILE")
echo "✅ Backup created: $BACKUP_FILE ($BACKUP_SIZE bytes)"

# Verify backup integrity
echo ""
echo "4. Verifying backup integrity..."
if [ "$BACKUP_SIZE" -lt 200000 ]; then
    echo "❌ Backup file seems too small ($BACKUP_SIZE bytes)"
    echo "This might indicate a problem. Aborting for safety."
    exit 1
fi
echo "✅ Backup size looks good"

# Compare current vs new VBIOS
echo ""
echo "5. VBIOS Comparison..."
echo "Current VBIOS: $BACKUP_SIZE bytes"
echo "New VBIOS: $VBIOS_SIZE bytes"

# Final confirmation
echo ""
echo "6. Final Confirmation..."
echo "About to flash:"
echo "  Source: $VBIOS_FILE"
echo "  Target: GPU 0 (RX 6800 XT)"
echo "  Backup: $BACKUP_FILE"
echo ""
echo "⚠️  LAST WARNING: This will modify your GPU firmware!"
echo "⚠️  If something goes wrong, you'll need the backup to recover!"
echo ""
echo "Type 'FLASH' (all caps) to proceed, or anything else to abort:"
read -r confirmation

if [ "$confirmation" != "FLASH" ]; then
    echo "❌ Aborted by user"
    exit 1
fi

# Perform the flash
echo ""
echo "7. Flashing VBIOS..."
echo "🔥 FLASHING IN PROGRESS - DO NOT INTERRUPT!"
echo ""

# Flash the VBIOS
amdvbflash -f -p 0 "$VBIOS_FILE"

echo ""
echo "8. Flash Complete!"
echo "✅ VBIOS has been flashed successfully"
echo ""
echo "IMPORTANT NEXT STEPS:"
echo "1. DO NOT REBOOT YET - verify the flash first"
echo "2. Run: amdvbflash -i (to verify new VBIOS is loaded)"
echo "3. Check for any error messages"
echo "4. Only reboot if verification looks good"
echo ""
echo "If anything looks wrong:"
echo "  Recovery command: amdvbflash -f -p 0 $BACKUP_FILE"
echo ""
echo "Backup location: $(pwd)/$BACKUP_FILE"
echo "Keep this backup file safe!"
