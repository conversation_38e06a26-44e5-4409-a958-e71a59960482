#!/bin/bash

# Find Compatible VBIOS Information
echo "=== Compatible VBIOS Finder ==="
echo ""

echo "Your GPU Information:"
echo "Device ID: $(cat /sys/class/drm/card0/device/device)"
echo "Vendor ID: $(cat /sys/class/drm/card0/device/vendor)"
echo "Subsystem Device: $(cat /sys/class/drm/card0/device/subsystem_device)"
echo "Subsystem Vendor: $(cat /sys/class/drm/card0/device/subsystem_vendor)"
echo "Current VBIOS: 113-438MI-U89"
echo ""

echo "=== VBIOS Compatibility Analysis ==="
echo ""
echo "Your card needs a VBIOS with:"
echo "- Device ID: 73BF (RX 6800 XT)"
echo "- Subsystem ID: 438E (your specific Sapphire variant)"
echo "- Subsystem Vendor: 1DA2 (Sapphire)"
echo ""

echo "The VBIOS files you tried:"
echo "1. Sapphire.RX6800XT.16384.210112.rom - SSID mismatch (different variant)"
echo "2. 267602.rom - SSID E438 vs your 438E (reversed, different variant)"
echo ""

echo "=== Solutions ==="
echo ""
echo "Option 1: Find Exact Match VBIOS"
echo "Search TechPowerUp database for:"
echo "- Sapphire RX 6800 XT"
echo "- Subsystem ID: 438E"
echo "- Look for Gaming/XT version (not Mining)"
echo ""

echo "Option 2: Override SSID Check (RISKY)"
echo "Some flash tools can ignore SSID mismatch:"
echo "- Windows ATIFlash with -f flag"
echo "- Specialized GPU flashing tools"
echo ""

echo "Option 3: Modify VBIOS SSID"
echo "Edit the VBIOS file to match your SSID:"
echo "- Change E438 to 438E in hex editor"
echo "- Recalculate checksums"
echo "- Flash modified VBIOS"
echo ""

echo "Option 4: Hardware Override"
echo "Check your GPU for:"
echo "- Dual BIOS switch (allows safe experimentation)"
echo "- BIOS recovery jumpers"
echo ""

echo "=== Recommended Next Steps ==="
echo ""
echo "1. **Search for exact VBIOS match:**"
echo "   Go to: https://www.techpowerup.com/vgabios/"
echo "   Search: Sapphire RX 6800 XT"
echo "   Filter: Subsystem 438E or similar"
echo "   Download: Gaming/XT version with version >U89"
echo ""

echo "2. **Try Windows flashing:**"
echo "   Boot Windows, use ATIFlash with -f flag"
echo "   Command: atiflash -f -p 0 vbios.rom"
echo ""

echo "3. **Contact Sapphire:**"
echo "   Request official VBIOS for your exact model"
echo "   Mention subsystem ID 438E and current mining BIOS issue"
echo ""

echo "4. **Check physical BIOS switch:**"
echo "   Look near power connectors for small switch"
echo "   If present, you can safely experiment"
echo ""

echo "Your current mining BIOS (113-438MI-U89) is limiting VRAM to ~1000MHz"
echo "A proper gaming BIOS should unlock 1500-2000MHz states"
echo ""
echo "Backup files created:"
ls -la *backup*.rom 2>/dev/null || echo "No backup files found"
