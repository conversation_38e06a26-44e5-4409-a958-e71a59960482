#!/bin/bash
# Setup script for Intelligent Case Fan Control System
# For ASRock B660M-ITX/ac motherboard

set -euo pipefail

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${GREEN}✅${NC} $1"; }
print_warning() { echo -e "${YELLOW}⚠️${NC} $1"; }
print_error() { echo -e "${RED}❌${NC} $1"; }
print_info() { echo -e "${BLUE}ℹ️${NC} $1"; }

# Check if running as root
if [[ $EUID -ne 0 ]]; then
    print_error "This script must be run as root"
    echo "Usage: sudo ./setup_intelligent_fan_control.sh"
    exit 1
fi

echo "🔧 Intelligent Case Fan Control Setup"
echo "====================================="
echo

# Check dependencies
print_info "Checking dependencies..."

if ! command -v sensors &> /dev/null; then
    print_error "lm-sensors not found. Installing..."
    pacman -S --noconfirm lm_sensors
fi

if ! command -v python3 &> /dev/null; then
    print_error "Python 3 not found. Please install python."
    exit 1
fi

print_status "Dependencies check complete"

# Check sensors configuration
print_info "Checking sensor configuration..."

if ! sensors &> /dev/null; then
    print_warning "Sensors not configured. Running sensors-detect..."
    echo "Press Enter at each prompt to accept defaults:"
    sensors-detect
    print_status "Sensors configuration complete"
fi

# Verify required sensors
print_info "Verifying required sensors..."

CPU_SENSOR=$(sensors | grep -c "coretemp" || echo "0")
GPU_SENSOR=$(sensors | grep -c "amdgpu" || echo "0")
MB_SENSOR=$(sensors | grep -c "nct6798" || echo "0")

if [[ $CPU_SENSOR -eq 0 ]]; then
    print_error "CPU temperature sensor (coretemp) not found"
    exit 1
fi

if [[ $GPU_SENSOR -eq 0 ]]; then
    print_warning "GPU temperature sensor (amdgpu) not found - GPU monitoring disabled"
fi

if [[ $MB_SENSOR -eq 0 ]]; then
    print_error "Motherboard sensor (nct6798) not found - cannot control fans"
    exit 1
fi

print_status "Required sensors found"

# Check fan control capability
print_info "Testing fan control capability..."

FAN_CONTROL_AVAILABLE=false
for fan_num in 2 3 4 5; do
    PWM_PATH="/sys/class/hwmon/hwmon*/pwm${fan_num}"
    if ls $PWM_PATH &> /dev/null; then
        FAN_CONTROL_AVAILABLE=true
        print_status "Found controllable fan: pwm${fan_num}"
    fi
done

if [[ $FAN_CONTROL_AVAILABLE == false ]]; then
    print_error "No controllable chassis fans found"
    print_info "Make sure chassis fans are connected to motherboard headers"
    exit 1
fi

# Install the script
print_info "Installing intelligent fan control script..."

cp intelligent_fan_control.py /usr/local/bin/
chmod +x /usr/local/bin/intelligent_fan_control.py
print_status "Script installed to /usr/local/bin/"

# Install systemd service
print_info "Installing systemd service..."

cp intelligent-fan-control.service /etc/systemd/system/
systemctl daemon-reload
print_status "Service installed"

# Test the script
print_info "Testing the fan control script..."

echo "Running 30-second test..."
timeout 30 python3 /usr/local/bin/intelligent_fan_control.py 2 || {
    if [[ $? -eq 124 ]]; then
        print_status "Test completed successfully"
    else
        print_error "Test failed - check permissions and hardware"
        exit 1
    fi
}

# Offer to enable service
echo
read -p "Enable intelligent fan control service to start at boot? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    systemctl enable intelligent-fan-control.service
    print_status "Service enabled for automatic startup"
    
    read -p "Start the service now? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        systemctl start intelligent-fan-control.service
        print_status "Service started"
        
        echo
        print_info "Monitor the service with:"
        echo "  sudo systemctl status intelligent-fan-control"
        echo "  sudo journalctl -u intelligent-fan-control -f"
    fi
else
    print_info "Service not enabled. You can enable it later with:"
    echo "  sudo systemctl enable --now intelligent-fan-control.service"
fi

echo
print_status "Setup complete!"
echo
echo "📊 Usage Information:"
echo "===================="
echo
echo "🔧 Manual Control:"
echo "  sudo python3 /usr/local/bin/intelligent_fan_control.py [interval]"
echo
echo "🔄 Service Management:"
echo "  sudo systemctl start intelligent-fan-control    # Start service"
echo "  sudo systemctl stop intelligent-fan-control     # Stop service"
echo "  sudo systemctl status intelligent-fan-control   # Check status"
echo "  sudo systemctl enable intelligent-fan-control   # Enable at boot"
echo "  sudo systemctl disable intelligent-fan-control  # Disable at boot"
echo
echo "📈 Monitoring:"
echo "  sudo journalctl -u intelligent-fan-control -f   # Live logs"
echo "  watch -n 1 'sensors | grep -E \"(fan|temp)\"'    # Watch sensors"
echo
echo "🛠️ Configuration:"
echo "  Edit /usr/local/bin/intelligent_fan_control.py to adjust fan curves"
echo "  Modify temp_points and speed_points in the FanCurve class"
echo
echo "⚠️ Important Notes:"
echo "  - The service automatically restores BIOS fan control on exit"
echo "  - Fan curves are conservative by default for quiet operation"
echo "  - Logs are written to /var/log/intelligent-fan-control.log"
echo "  - The system monitors both CPU and GPU, using the hottest temperature"
echo
print_status "Intelligent fan control system is ready!"
