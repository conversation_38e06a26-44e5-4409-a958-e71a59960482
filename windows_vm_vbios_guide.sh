#!/bin/bash

# Windows VM VBIOS Modification Guide
echo "=== Windows VM for VBIOS Modification ==="
echo ""

echo "🟡 SAFER APPROACH: Use VM for VBIOS editing, flash in Linux"
echo ""

echo "=== Step 1: VM Setup for VBIOS Editing ==="
echo ""
echo "You DON'T need GPU passthrough for VBIOS editing!"
echo "A simple Windows VM can run the editing tools:"
echo ""
echo "1. **Create Windows VM:**"
echo "   - VirtualBox or VMware"
echo "   - Windows 10/11 (any edition)"
echo "   - 4GB RAM, 50GB disk minimum"
echo "   - NO GPU passthrough needed"
echo ""

echo "2. **Download tools in VM:**"
echo "   - MorePowerTool: https://www.igorslab.de/morepowertool/"
echo "   - GPU-Z: https://www.techpowerup.com/gpuz/"
echo "   - Red BIOS Editor (alternative)"
echo ""

echo "3. **Transfer your VBIOS backup to VM:**"
echo "   - Copy: original_vbios_backup_*.rom"
echo "   - Use shared folders or USB"
echo ""

echo "=== Step 2: VBIOS Modification in VM ==="
echo ""
echo "1. **Open MorePowerTool in Windows VM**"
echo "2. **Load your VBIOS backup file**"
echo "3. **Navigate to Memory tab**"
echo "4. **Add missing memory states:**"
echo "   - State 4: 1500MHz"
echo "   - State 5: 1750MHz"
echo "   - State 6: 2000MHz"
echo "5. **Adjust memory timings if needed**"
echo "6. **Save as modified_vbios.rom**"
echo "7. **Transfer back to Linux**"
echo ""

echo "=== Step 3: Flash Modified VBIOS in Linux ==="
echo ""
echo "Back in Linux, flash the VM-modified VBIOS:"
echo "sudo amdvbflash -p 0 modified_vbios.rom"
echo ""

echo "=== Alternative: Full GPU Passthrough (ADVANCED) ==="
echo ""
echo "If you want to flash directly in VM:"
echo ""

echo "**Requirements:**"
echo "- CPU with IOMMU support (Intel VT-d or AMD-Vi)"
echo "- Motherboard IOMMU support"
echo "- Second GPU for host (or integrated graphics)"
echo "- VFIO-PCI setup"
echo ""

echo "**Setup commands (Arch Linux):**"
cat << 'EOF'
# 1. Enable IOMMU in GRUB
sudo sed -i 's/GRUB_CMDLINE_LINUX_DEFAULT="/&intel_iommu=on iommu=pt /' /etc/default/grub
# OR for AMD: amd_iommu=on iommu=pt

# 2. Update GRUB
sudo grub-mkconfig -o /boot/grub/grub.cfg

# 3. Install VFIO modules
echo 'vfio-pci' | sudo tee -a /etc/modules-load.d/vfio.conf

# 4. Find GPU PCI ID
lspci -nn | grep VGA

# 5. Bind GPU to VFIO (replace with your GPU ID)
echo 'options vfio-pci ids=1002:73bf,1002:ab28' | sudo tee /etc/modprobe.d/vfio.conf

# 6. Reboot
EOF

echo ""
echo "**VM Configuration:**"
echo "- QEMU/KVM with GPU passthrough"
echo "- Windows 10/11 guest"
echo "- GPU passed through to VM"
echo "- Install AMD drivers in VM"
echo "- Use ATIFlash in VM"
echo ""

echo "=== Risk Assessment ==="
echo ""

echo "🟢 **LOW RISK: VM VBIOS Editing**"
echo "- Edit VBIOS in safe VM environment"
echo "- Flash in Linux with recovery options"
echo "- Can test modifications before flashing"
echo ""

echo "🟡 **MEDIUM RISK: GPU Passthrough**"
echo "- Complex setup process"
echo "- Requires compatible hardware"
echo "- Host loses GPU access during VM use"
echo ""

echo "🔴 **HIGH RISK: VM Flashing**"
echo "- Limited recovery if flash fails"
echo "- Hardware detection issues possible"
echo "- Driver conflicts during flash"
echo ""

echo "=== Recommended Workflow ==="
echo ""

echo "**SAFEST APPROACH:**"
echo "1. ✅ Use simple VM for VBIOS editing only"
echo "2. ✅ Transfer modified VBIOS to Linux"
echo "3. ✅ Flash in Linux with proper backups"
echo "4. ✅ Keep recovery options available"
echo ""

echo "**Why this is safer:**"
echo "- VM crash won't affect GPU"
echo "- Linux has better recovery tools"
echo "- Can test VBIOS before flashing"
echo "- Easier to revert if needed"
echo ""

echo "=== VM Setup Script ==="
echo ""

echo "Want to set up a Windows VM for VBIOS editing?"
echo "This will install VirtualBox and help configure it:"
echo ""

read -p "Install VirtualBox for VBIOS editing VM? (y/N): " install_vm

if [[ "$install_vm" =~ ^[Yy]$ ]]; then
    echo ""
    echo "Installing VirtualBox..."
    
    # Install VirtualBox
    if command -v pacman &> /dev/null; then
        sudo pacman -S virtualbox virtualbox-host-modules-arch
        sudo modprobe vboxdrv
    elif command -v apt &> /dev/null; then
        sudo apt update
        sudo apt install virtualbox virtualbox-ext-pack
    else
        echo "Please install VirtualBox manually for your distribution"
        exit 1
    fi
    
    echo ""
    echo "✅ VirtualBox installed!"
    echo ""
    echo "Next steps:"
    echo "1. Download Windows 10/11 ISO"
    echo "2. Create VM with 4GB RAM, 50GB disk"
    echo "3. Install Windows in VM"
    echo "4. Download MorePowerTool in VM"
    echo "5. Copy your VBIOS backup to VM"
    echo "6. Edit VBIOS and copy modified version back"
    echo ""
    echo "VM will NOT need GPU passthrough for editing!"
    
else
    echo ""
    echo "Manual VM setup:"
    echo "- Install VirtualBox or VMware"
    echo "- Create Windows VM (no special GPU config needed)"
    echo "- Use for VBIOS editing only"
fi

echo ""
echo "=== Files You'll Need ==="
echo ""
echo "Transfer these to your VM:"
ls -la original_vbios_backup_*.rom 2>/dev/null || echo "- Your VBIOS backup files"
echo ""
echo "Download in VM:"
echo "- MorePowerTool from Igor's Lab"
echo "- GPU-Z for verification"
echo ""
echo "The modified VBIOS can then be flashed safely in Linux!"
