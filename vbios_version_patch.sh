#!/bin/bash

# VBIOS Version Patching to bypass version check
echo "=== VBIOS Version Patching ==="

ORIGINAL_VBIOS="Sapphire.RX6800XT.16384.210112.rom"
PATCHED_VBIOS="Sapphire.RX6800XT.16384.210112.patched.rom"

echo "Creating patched VBIOS with newer version string..."

# Copy original to patched version
cp "$ORIGINAL_VBIOS" "$PATCHED_VBIOS"

# Change version from U88 to U90 (newer than current U89)
echo "Patching version string from 113-438XT-U88 to 113-438XT-U90..."
sed -i 's/113-438XT-U88/113-438XT-U90/g' "$PATCHED_VBIOS"

# Verify the patch
echo "Verifying patch..."
if strings "$PATCHED_VBIOS" | grep -q "113-438XT-U90"; then
    echo "✅ Version string successfully patched to U90"
else
    echo "❌ Patch failed"
    exit 1
fi

echo ""
echo "Patched VBIOS created: $PATCHED_VBIOS"
echo "Original version: 113-438XT-U88"
echo "Patched version:  113-438XT-U90"
echo ""
echo "Now attempting flash with patched VBIOS..."

# Try flashing the patched version
sudo amdvbflash -p 0 "$PATCHED_VBIOS"

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 FLASH SUCCESSFUL!"
    echo ""
    echo "Verifying new VBIOS..."
    sudo amdvbflash -i
    
    echo ""
    echo "⚠️  IMPORTANT: Reboot required to activate new VBIOS"
    echo "After reboot, check VRAM frequency states with:"
    echo "  cat /sys/class/drm/card0/device/pp_dpm_mclk"
    
else
    echo ""
    echo "❌ Flash still failed. Error code: $?"
    echo ""
    echo "Alternative options:"
    echo "1. Try flashing from Windows with ATIFlash"
    echo "2. Use hardware BIOS switch (if available)"
    echo "3. Try different VBIOS file"
    echo "4. Contact Sapphire for official VBIOS update"
fi
