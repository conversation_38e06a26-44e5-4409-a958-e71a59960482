#!/usr/bin/env python3
"""
Intelligent Case Fan Control System
Automatically adjusts chassis fan speeds based on the highest temperature 
between CPU and GPU, providing optimal cooling for whichever component 
is working hardest.

For ASRock B660M-ITX/ac with NCT6798 sensor chip
"""

import subprocess
import time
import sys
import signal
import logging
import re
from pathlib import Path
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple

@dataclass
class FanCurve:
    """Temperature to fan speed mapping"""
    temp_points: List[int]      # Temperature thresholds in °C
    speed_points: List[int]     # Fan speeds in PWM (0-255)
    hysteresis: int = 3         # Temperature drop needed before reducing speed

class IntelligentFanController:
    def __init__(self, config_file: Optional[str] = None):
        self.running = False
        self.last_pwm_values = {}
        self.last_max_temp = 0
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('/var/log/intelligent-fan-control.log')
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # Find hardware paths
        self.hwmon_paths = self._find_hwmon_paths()
        self.fan_paths = self._find_fan_paths()
        
        # Optimized fan curve for Noctua NF-A14x25 G2 PWM fans
        # These are high-quality fans that can run very quietly at low speeds
        self.fan_curve = FanCurve(
            temp_points=[35, 45, 55, 65, 75, 85, 95],
            speed_points=[20, 35, 60, 100, 150, 200, 255],  # PWM values (0-255)
            hysteresis=4  # Slightly higher hysteresis for stable operation
        )
        
        self.logger.info("Intelligent Fan Controller initialized")
        self.logger.info(f"Found hwmon paths: {self.hwmon_paths}")
        self.logger.info(f"Found fan paths: {self.fan_paths}")
    
    def _find_hwmon_paths(self) -> Dict[str, str]:
        """Find hwmon device paths for CPU and GPU sensors"""
        paths = {}
        
        # Find CPU temperature sensor (coretemp)
        for hwmon_dir in Path("/sys/class/hwmon").glob("hwmon*"):
            name_file = hwmon_dir / "name"
            if name_file.exists():
                name = name_file.read_text().strip()
                if name == "coretemp":
                    paths["cpu"] = str(hwmon_dir)
                elif name == "amdgpu":
                    paths["gpu"] = str(hwmon_dir)
                elif name == "nct6798":
                    paths["motherboard"] = str(hwmon_dir)
        
        return paths
    
    def _find_fan_paths(self) -> Dict[str, str]:
        """Find controllable fan PWM paths on motherboard"""
        fan_paths = {}
        
        if "motherboard" not in self.hwmon_paths:
            self.logger.error("NCT6798 motherboard sensor not found")
            return fan_paths
        
        mb_path = Path(self.hwmon_paths["motherboard"])
        
        # Check for available PWM controls (chassis fans)
        # fan1 = CPU fans (2x with Y-connector) - leave in auto
        # fan2 = Noctua NF-A14x25 G2 PWM 140mm case fan
        # fan7 = Noctua NF-A14x25 G2 PWM 140mm case fan
        for fan_num in [2, 7]:  # Only control the two Noctua case fans
            pwm_file = mb_path / f"pwm{fan_num}"
            pwm_enable_file = mb_path / f"pwm{fan_num}_enable"

            if pwm_file.exists() and pwm_enable_file.exists():
                fan_name = "noctua_140mm_1" if fan_num == 2 else "noctua_140mm_2"
                fan_paths[fan_name] = {
                    "pwm": str(pwm_file),
                    "pwm_enable": str(pwm_enable_file),
                    "fan_num": fan_num
                }
        
        return fan_paths
    
    def _read_temperature(self, sensor_type: str) -> Optional[float]:
        """Read temperature from CPU or GPU sensor"""
        if sensor_type not in self.hwmon_paths:
            return None
        
        hwmon_path = Path(self.hwmon_paths[sensor_type])
        
        try:
            if sensor_type == "cpu":
                # CPU package temperature
                temp_file = hwmon_path / "temp1_input"
            elif sensor_type == "gpu":
                # GPU edge temperature
                temp_file = hwmon_path / "temp1_input"
            else:
                return None
            
            if temp_file.exists():
                temp_millidegrees = int(temp_file.read_text().strip())
                return temp_millidegrees / 1000.0
        except (ValueError, FileNotFoundError, PermissionError) as e:
            self.logger.warning(f"Failed to read {sensor_type} temperature: {e}")
        
        return None
    
    def _get_max_temperature(self) -> Tuple[float, str]:
        """Get the highest temperature between CPU and GPU"""
        cpu_temp = self._read_temperature("cpu")
        gpu_temp = self._read_temperature("gpu")
        
        temps = {}
        if cpu_temp is not None:
            temps["CPU"] = cpu_temp
        if gpu_temp is not None:
            temps["GPU"] = gpu_temp
        
        if not temps:
            self.logger.error("No temperature readings available")
            return 0.0, "unknown"
        
        max_component = max(temps, key=temps.get)
        max_temp = temps[max_component]
        
        return max_temp, max_component
    
    def _calculate_fan_speed(self, temperature: float) -> int:
        """Calculate PWM value based on temperature using fan curve with hysteresis"""
        # Apply hysteresis - only reduce speed if temp drops significantly
        if temperature < self.last_max_temp - self.fan_curve.hysteresis:
            effective_temp = temperature
        else:
            effective_temp = max(temperature, self.last_max_temp)
        
        # Find appropriate PWM value from curve
        for i, temp_point in enumerate(self.fan_curve.temp_points):
            if effective_temp <= temp_point:
                return self.fan_curve.speed_points[i]
        
        # Temperature above highest point - use maximum speed
        return self.fan_curve.speed_points[-1]
    
    def _set_fan_speed(self, fan_name: str, pwm_value: int) -> bool:
        """Set fan speed via PWM control"""
        if fan_name not in self.fan_paths:
            return False
        
        fan_config = self.fan_paths[fan_name]
        
        try:
            # Enable manual control
            with open(fan_config["pwm_enable"], 'w') as f:
                f.write("1")
            
            # Set PWM value
            with open(fan_config["pwm"], 'w') as f:
                f.write(str(pwm_value))
            
            return True
        except (PermissionError, FileNotFoundError) as e:
            self.logger.error(f"Failed to set {fan_name} speed: {e}")
            return False
    
    def _restore_auto_control(self):
        """Restore automatic fan control"""
        self.logger.info("Restoring automatic fan control...")
        
        for fan_name, fan_config in self.fan_paths.items():
            try:
                with open(fan_config["pwm_enable"], 'w') as f:
                    f.write("2")  # Automatic mode
                self.logger.info(f"Restored auto control for {fan_name}")
            except Exception as e:
                self.logger.error(f"Failed to restore auto control for {fan_name}: {e}")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        self.logger.info(f"Received signal {signum}, shutting down...")
        self.running = False
    
    def run(self, interval: int = 5):
        """Main control loop"""
        # Setup signal handlers
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)
        
        if not self.fan_paths:
            self.logger.error("No controllable fans found. Exiting.")
            return
        
        self.logger.info("Starting intelligent fan control...")
        self.logger.info(f"Fan curve: {self.fan_curve.temp_points}°C -> {self.fan_curve.speed_points} PWM")
        self.logger.info(f"Controllable fans: {list(self.fan_paths.keys())}")
        
        self.running = True
        
        try:
            while self.running:
                # Get maximum temperature
                max_temp, hottest_component = self._get_max_temperature()
                
                if max_temp > 0:
                    # Calculate required fan speed
                    target_pwm = self._calculate_fan_speed(max_temp)
                    
                    # Apply to all chassis fans
                    for fan_name in self.fan_paths:
                        if self._set_fan_speed(fan_name, target_pwm):
                            self.last_pwm_values[fan_name] = target_pwm
                    
                    # Log status
                    pwm_percent = int(target_pwm * 100 / 255)
                    self.logger.info(
                        f"Max temp: {max_temp:.1f}°C ({hottest_component}) -> "
                        f"Fan speed: {target_pwm}/255 ({pwm_percent}%)"
                    )
                    
                    self.last_max_temp = max_temp
                else:
                    self.logger.warning("No valid temperature readings")
                
                time.sleep(interval)
        
        except Exception as e:
            self.logger.error(f"Unexpected error: {e}")
        
        finally:
            self._restore_auto_control()
            self.logger.info("Intelligent fan control stopped")

def main():
    if len(sys.argv) > 1 and sys.argv[1] in ["-h", "--help"]:
        print("""
Intelligent Case Fan Control System

Usage: sudo python3 intelligent_fan_control.py [interval]

Arguments:
  interval    Update interval in seconds (default: 5)

Features:
- Monitors both CPU and GPU temperatures
- Controls chassis fans based on hottest component
- Conservative fan curve for quiet operation
- Automatic restoration of BIOS control on exit
- Hysteresis to prevent rapid speed changes

Requirements:
- Run as root for hardware access
- ASRock B660M-ITX/ac motherboard with NCT6798 sensor
- lm-sensors installed and configured
        """)
        return
    
    # Check if running as root
    if os.geteuid() != 0:
        print("Error: This script must be run as root for hardware access")
        print("Usage: sudo python3 intelligent_fan_control.py")
        sys.exit(1)
    
    # Parse interval argument
    interval = 5
    if len(sys.argv) > 1:
        try:
            interval = int(sys.argv[1])
            if interval < 1:
                raise ValueError
        except ValueError:
            print("Error: Interval must be a positive integer")
            sys.exit(1)
    
    # Create and run controller
    controller = IntelligentFanController()
    controller.run(interval)

if __name__ == "__main__":
    import os
    main()
