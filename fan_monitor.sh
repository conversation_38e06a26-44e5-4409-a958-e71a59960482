#!/bin/bash
# Fan Monitoring Script (Bash version)
# Monitors all system fans for 10 minutes to identify intermittent fan behavior
# Usage: ./fan_monitor.sh [output_file.csv]

set -euo pipefail

# Configuration
OUTPUT_FILE="${1:-fan_monitoring_$(date +%Y%m%d_%H%M%S).csv}"
SAMPLE_COUNT=600  # 10 minutes
SAMPLE_INTERVAL=1 # seconds
TEMP_FILE="/tmp/fan_sensors.tmp"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠️${NC} $1"
}

print_error() {
    echo -e "${RED}❌${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ️${NC} $1"
}

# Function to extract fan RPM from sensors output
get_fan_data() {
    sensors -u > "$TEMP_FILE" 2>/dev/null || {
        print_error "Failed to read sensors data"
        return 1
    }
    
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S.%3N')
    local mb_fan1=$(grep -A1 "nct6798" -A50 "$TEMP_FILE" | grep "fan1_input:" | head -1 | awk '{print int($2)}' || echo "0")
    local mb_fan2=$(grep -A1 "nct6798" -A50 "$TEMP_FILE" | grep "fan2_input:" | awk '{print int($2)}' || echo "0")
    local mb_fan3=$(grep -A1 "nct6798" -A50 "$TEMP_FILE" | grep "fan3_input:" | awk '{print int($2)}' || echo "0")
    local mb_fan4=$(grep -A1 "nct6798" -A50 "$TEMP_FILE" | grep "fan4_input:" | awk '{print int($2)}' || echo "0")
    local mb_fan5=$(grep -A1 "nct6798" -A50 "$TEMP_FILE" | grep "fan5_input:" | awk '{print int($2)}' || echo "0")
    local mb_fan7=$(grep -A1 "nct6798" -A50 "$TEMP_FILE" | grep "fan7_input:" | awk '{print int($2)}' || echo "0")
    local gpu_fan1=$(grep -A1 "amdgpu" -A20 "$TEMP_FILE" | grep "fan1_input:" | awk '{print int($2)}' || echo "0")
    
    echo "$timestamp,$mb_fan1,$mb_fan2,$mb_fan3,$mb_fan4,$mb_fan5,$mb_fan7,$gpu_fan1"
}

# Function to show progress bar
show_progress() {
    local current=$1
    local total=$2
    local percent=$((current * 100 / total))
    local filled=$((current * 40 / total))
    local empty=$((40 - filled))
    
    printf "\rCollecting data: ["
    printf "%*s" $filled | tr ' ' '█'
    printf "%*s" $empty | tr ' ' '-'
    printf "] %d/%d samples (%d%%)" $current $total $percent
}

# Function to analyze data and print summary
analyze_data() {
    if [[ ! -f "$OUTPUT_FILE" ]]; then
        print_error "Output file not found: $OUTPUT_FILE"
        return 1
    fi
    
    echo
    echo "================================================================"
    echo "📊 FAN MONITORING SUMMARY"
    echo "================================================================"
    
    local total_samples=$(tail -n +2 "$OUTPUT_FILE" | wc -l)
    echo "Total samples collected: $total_samples"
    echo
    
    # Analyze each fan column
    local fan_names=("MB_CPU_Fan" "MB_Chassis_Fan1" "MB_Chassis_Fan2" "MB_Chassis_Fan3" "MB_Chassis_Fan4" "MB_Pump_Fan" "GPU_Fan")
    
    for i in {2..8}; do
        local fan_name="${fan_names[$((i-2))]}"
        echo "🔧 $fan_name:"
        
        # Extract column data (skip header)
        local fan_data=$(tail -n +2 "$OUTPUT_FILE" | cut -d',' -f$i)
        local non_zero_count=$(echo "$fan_data" | grep -v '^0$' | wc -l)
        local zero_count=$(echo "$fan_data" | grep '^0$' | wc -l)
        
        echo "   Active samples: $non_zero_count"
        echo "   Zero RPM samples: $zero_count"
        
        if [[ $non_zero_count -gt 0 ]]; then
            local min_rpm=$(echo "$fan_data" | grep -v '^0$' | sort -n | head -1)
            local max_rpm=$(echo "$fan_data" | grep -v '^0$' | sort -n | tail -1)
            local avg_rpm=$(echo "$fan_data" | grep -v '^0$' | awk '{sum+=$1} END {if(NR>0) print int(sum/NR); else print 0}')
            local range=$((max_rpm - min_rpm))
            
            echo "   Min RPM: $min_rpm"
            echo "   Max RPM: $max_rpm"
            echo "   Avg RPM: $avg_rpm"
            echo "   RPM Range: $range"
            
            # Check for high variation
            if [[ $range -gt 200 ]]; then
                print_warning "   HIGH VARIATION DETECTED! Range: $range RPM"
            fi
            
            # Count rapid changes (>100 RPM between consecutive samples)
            local rapid_changes=0
            local prev_rpm=""
            while IFS= read -r rpm; do
                if [[ -n "$prev_rpm" && "$rpm" != "0" && "$prev_rpm" != "0" ]]; then
                    local diff=$((rpm > prev_rpm ? rpm - prev_rpm : prev_rpm - rpm))
                    if [[ $diff -gt 100 ]]; then
                        ((rapid_changes++))
                    fi
                fi
                prev_rpm="$rpm"
            done <<< "$fan_data"
            
            if [[ $rapid_changes -gt 0 ]]; then
                print_warning "   RAPID CHANGES: $rapid_changes instances of >100 RPM change"
            fi
        else
            echo "   Status: Fan not active (0 RPM throughout monitoring)"
        fi
        echo
    done
}

# Main execution
main() {
    echo "🔍 Fan Monitoring Script Started"
    echo "📊 Collecting $SAMPLE_COUNT samples over 10 minutes"
    echo "💾 Output file: $OUTPUT_FILE"
    echo "⏱️  Sample interval: $SAMPLE_INTERVAL seconds"
    echo
    
    # Test sensor connectivity
    print_info "Testing sensor connectivity..."
    if ! sensors > /dev/null 2>&1; then
        print_error "Cannot read sensors. Please install lm-sensors: sudo pacman -S lm_sensors"
        exit 1
    fi
    
    # Show detected fans
    print_status "Sensor test successful. Detected fans:"
    sensors | grep -E "fan.*RPM" | while read -r line; do
        echo "   $line"
    done
    echo
    
    # Create CSV header
    echo "timestamp,MB_CPU_Fan,MB_Chassis_Fan1,MB_Chassis_Fan2,MB_Chassis_Fan3,MB_Chassis_Fan4,MB_Pump_Fan,GPU_Fan" > "$OUTPUT_FILE"
    
    # Start monitoring
    print_status "Starting data collection..."
    local start_time=$(date +%s)
    
    for ((i=1; i<=SAMPLE_COUNT; i++)); do
        local sample_start=$(date +%s.%3N)
        
        # Collect sample
        if ! get_fan_data >> "$OUTPUT_FILE"; then
            print_warning "Failed to collect sample $i"
            continue
        fi
        
        # Show progress
        show_progress $i $SAMPLE_COUNT
        
        # Sleep for remaining interval
        local sample_end=$(date +%s.%3N)
        local elapsed=$(echo "$sample_end - $sample_start" | bc -l 2>/dev/null || echo "0")
        local sleep_time=$(echo "$SAMPLE_INTERVAL - $elapsed" | bc -l 2>/dev/null || echo "1")
        
        if (( $(echo "$sleep_time > 0" | bc -l 2>/dev/null || echo "1") )); then
            sleep "$sleep_time" 2>/dev/null || sleep 1
        fi
    done
    
    local end_time=$(date +%s)
    local total_time=$((end_time - start_time))
    
    echo
    echo
    print_status "Data collection complete!"
    echo "📈 Collected $SAMPLE_COUNT samples in ${total_time} seconds"
    echo "💾 Data saved to: $OUTPUT_FILE"
    
    # Analyze results
    analyze_data
    
    echo
    echo "🔍 ANALYSIS SUGGESTIONS:"
    echo "1. Open $OUTPUT_FILE in LibreOffice Calc or Excel"
    echo "2. Create line graphs for each fan column to visualize behavior"
    echo "3. Look for sudden spikes or drops in RPM values"
    echo "4. Check for fans with high variation or rapid changes"
    echo
    echo "📈 Quick analysis commands:"
    echo "   grep -v ',0,' $OUTPUT_FILE | head -10  # Show active fan samples"
    echo "   cut -d',' -f2 $OUTPUT_FILE | tail -n +2 | sort -n | uniq -c  # CPU fan RPM distribution"
    echo "   tail -f $OUTPUT_FILE  # Monitor in real-time (if running)"
    
    # Cleanup
    rm -f "$TEMP_FILE"
}

# Handle Ctrl+C gracefully
trap 'echo -e "\n\n⚠️ Monitoring interrupted by user"; analyze_data; exit 0' INT

# Check dependencies
if ! command -v sensors &> /dev/null; then
    print_error "sensors command not found. Please install lm-sensors:"
    echo "   sudo pacman -S lm_sensors"
    echo "   sudo sensors-detect --auto"
    exit 1
fi

if ! command -v bc &> /dev/null; then
    print_warning "bc not found. Install for better timing: sudo pacman -S bc"
fi

# Run main function
main "$@"
