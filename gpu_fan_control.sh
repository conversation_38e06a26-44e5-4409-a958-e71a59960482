#!/bin/bash
# Manual GPU Fan Control Script
# Sets conservative fan curve to prevent zero RPM cycling

set -euo pipefail

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# GPU paths
GPU_PATH="/sys/class/drm/card0/device"
HWMON_PATH="/sys/class/hwmon"

print_status() { echo -e "${GREEN}✅${NC} $1"; }
print_warning() { echo -e "${YELLOW}⚠️${NC} $1"; }
print_error() { echo -e "${RED}❌${NC} $1"; }

# Find AMD GPU hwmon device
find_amd_hwmon() {
    for hwmon in $HWMON_PATH/hwmon*; do
        if [[ -f "$hwmon/name" ]] && grep -q "amdgpu" "$hwmon/name" 2>/dev/null; then
            echo "$hwmon"
            return 0
        fi
    done
    return 1
}

# Set manual fan control
enable_manual_control() {
    local hwmon_path="$1"
    
    if [[ -f "$hwmon_path/pwm1_enable" ]]; then
        echo 1 | sudo tee "$hwmon_path/pwm1_enable" > /dev/null
        print_status "Manual fan control enabled"
        return 0
    else
        print_error "Cannot find pwm1_enable file"
        return 1
    fi
}

# Set fan speed (0-255)
set_fan_speed() {
    local hwmon_path="$1"
    local speed="$2"  # 0-255
    
    if [[ -f "$hwmon_path/pwm1" ]]; then
        echo "$speed" | sudo tee "$hwmon_path/pwm1" > /dev/null
        local percent=$((speed * 100 / 255))
        print_status "Fan speed set to $speed/255 ($percent%)"
        return 0
    else
        print_error "Cannot find pwm1 file"
        return 1
    fi
}

# Get current temperature
get_gpu_temp() {
    local hwmon_path="$1"
    
    if [[ -f "$hwmon_path/temp1_input" ]]; then
        local temp_millidegrees=$(cat "$hwmon_path/temp1_input")
        local temp_celsius=$((temp_millidegrees / 1000))
        echo "$temp_celsius"
    else
        echo "0"
    fi
}

# Very conservative fan curve function
calculate_fan_speed() {
    local temp="$1"
    local fan_speed

    if [[ $temp -le 35 ]]; then
        fan_speed=13    # ~5% minimum (13/255 ≈ 5% = ~170 RPM)
    elif [[ $temp -le 40 ]]; then
        fan_speed=20    # ~8% (20/255 ≈ 8% = ~270 RPM)
    elif [[ $temp -le 45 ]]; then
        fan_speed=31    # ~12% (~400 RPM)
    elif [[ $temp -le 50 ]]; then
        fan_speed=46    # ~18% (~610 RPM)
    elif [[ $temp -le 55 ]]; then
        fan_speed=64    # ~25% (~850 RPM)
    elif [[ $temp -le 60 ]]; then
        fan_speed=89    # ~35%
    elif [[ $temp -le 65 ]]; then
        fan_speed=128   # ~50%
    elif [[ $temp -le 70 ]]; then
        fan_speed=166   # ~65%
    elif [[ $temp -le 75 ]]; then
        fan_speed=204   # ~80%
    elif [[ $temp -le 80 ]]; then
        fan_speed=242   # ~95%
    else
        fan_speed=255   # 100%
    fi

    echo "$fan_speed"
}

# Main execution
main() {
    echo "🔧 AMD GPU Conservative Fan Control"
    echo "=================================="
    
    # Find AMD GPU hwmon device
    print_status "Looking for AMD GPU..."
    if ! HWMON_PATH=$(find_amd_hwmon); then
        print_error "AMD GPU hwmon device not found"
        exit 1
    fi
    
    print_status "Found AMD GPU at: $HWMON_PATH"
    
    # Check current temperature
    CURRENT_TEMP=$(get_gpu_temp "$HWMON_PATH")
    print_status "Current GPU temperature: ${CURRENT_TEMP}°C"
    
    # Enable manual control
    if ! enable_manual_control "$HWMON_PATH"; then
        print_error "Failed to enable manual fan control"
        exit 1
    fi
    
    # Set conservative fan speed based on current temperature
    FAN_SPEED=$(calculate_fan_speed "$CURRENT_TEMP")
    if set_fan_speed "$HWMON_PATH" "$FAN_SPEED"; then
        print_status "Conservative fan curve applied"
    else
        print_error "Failed to set fan speed"
        exit 1
    fi
    
    echo
    print_warning "Manual fan control is now active"
    print_warning "To restore automatic control, run:"
    echo "  echo 2 | sudo tee $HWMON_PATH/pwm1_enable"
    echo
    print_status "Monitor with: watch -n 1 'sensors amdgpu-pci-0300'"
}

# Handle Ctrl+C to restore automatic control
trap 'echo; print_warning "Restoring automatic fan control..."; echo 2 | sudo tee $HWMON_PATH/pwm1_enable > /dev/null; print_status "Automatic control restored"; exit 0' INT

main "$@"
