#!/bin/bash

# AMD RX 6800 XT VRAM Frequency Fix Script
# This script applies multiple fixes to restore proper VRAM frequency scaling

echo "=== AMD RX 6800 XT VRAM Frequency Fix ==="
echo "Current VRAM frequency state:"
cat /sys/class/drm/card0/device/pp_dpm_mclk
echo ""

# Fix 1: Reset to auto mode first
echo "Resetting power management to auto..."
sudo sh -c 'echo "auto" > /sys/class/drm/card0/device/power_dpm_force_performance_level'

# Fix 2: Try manual performance mode
echo "Setting manual performance mode..."
sudo sh -c 'echo "manual" > /sys/class/drm/card0/device/power_dpm_force_performance_level'

# Fix 3: Force highest available memory state
echo "Forcing highest memory state..."
sudo sh -c 'echo "3" > /sys/class/drm/card0/device/pp_dpm_mclk'

echo "New VRAM frequency state:"
cat /sys/class/drm/card0/device/pp_dpm_mclk
echo ""

# Fix 4: Check if we can enable additional memory states via power profile
echo "Trying COMPUTE power profile (may unlock higher memory states)..."
sudo sh -c 'echo "5" > /sys/class/drm/card0/device/pp_power_profile_mode'

echo "VRAM states after COMPUTE profile:"
cat /sys/class/drm/card0/device/pp_dpm_mclk
echo ""

# Fix 5: Try custom power profile
echo "Trying CUSTOM power profile..."
sudo sh -c 'echo "6" > /sys/class/drm/card0/device/pp_power_profile_mode'

echo "VRAM states after CUSTOM profile:"
cat /sys/class/drm/card0/device/pp_dpm_mclk
echo ""

echo "=== Current Status ==="
echo "Power level: $(cat /sys/class/drm/card0/device/power_dpm_force_performance_level)"
echo "Power profile: $(cat /sys/class/drm/card0/device/pp_power_profile_mode | grep '*' | awk '{print $1 $2}')"
echo "GPU temp: $(($(cat /sys/class/drm/card0/device/hwmon/hwmon*/temp1_input)/1000))°C"
echo "VRAM temp: $(($(cat /sys/class/drm/card0/device/hwmon/hwmon*/temp2_input)/1000))°C"
echo "Power draw: $(($(cat /sys/class/drm/card0/device/hwmon/hwmon*/power1_average)/1000000))W"
