#!/bin/bash

# VRAM Frequency Monitoring Script
# Run this after reboot to verify the fix and monitor VRAM performance

echo "=== RX 6800 XT VRAM Frequency Monitor ==="
echo "Date: $(date)"
echo ""

# Check if kernel parameter is active
echo "1. Kernel Parameter Verification:"
if grep -q "amdgpu.ppfeaturemask=0xffffffff" /proc/cmdline; then
    echo "✓ AMDGPU feature mask parameter is active"
else
    echo "✗ AMDGPU feature mask parameter NOT found in kernel command line"
    echo "Current cmdline: $(cat /proc/cmdline)"
fi

echo ""
echo "2. Available VRAM Frequency States:"
cat /sys/class/drm/card0/device/pp_dpm_mclk

echo ""
echo "3. Current Power Management Settings:"
echo "Performance Level: $(cat /sys/class/drm/card0/device/power_dpm_force_performance_level)"
echo "Power Profile: $(cat /sys/class/drm/card0/device/pp_power_profile_mode | grep '*' | awk '{print $1 $2}')"

echo ""
echo "4. Current Temperatures and Power:"
echo "GPU Temperature: $(($(cat /sys/class/drm/card0/device/hwmon/hwmon*/temp1_input)/1000))°C"
echo "VRAM Temperature: $(($(cat /sys/class/drm/card0/device/hwmon/hwmon*/temp2_input)/1000))°C"
echo "Power Draw: $(($(cat /sys/class/drm/card0/device/hwmon/hwmon*/power1_average)/1000000))W"
echo "Power Limit: $(($(cat /sys/class/drm/card0/device/hwmon/hwmon*/power1_cap)/1000000))W"

echo ""
echo "5. Memory Information:"
echo "Total VRAM: $(($(cat /sys/class/drm/card0/device/mem_info_vram_total)/1024/1024/1024))GB"
echo "Used VRAM: $(($(cat /sys/class/drm/card0/device/mem_info_vram_used)/1024/1024))MB"

echo ""
echo "6. GPU Clock States:"
echo "GPU Frequencies:"
cat /sys/class/drm/card0/device/pp_dpm_sclk | head -5

echo ""
echo "=== Status Summary ==="
MCLK_STATES=$(cat /sys/class/drm/card0/device/pp_dpm_mclk | wc -l)
MAX_MCLK=$(cat /sys/class/drm/card0/device/pp_dpm_mclk | tail -1 | awk '{print $2}' | sed 's/Mhz//')

if [ "$MCLK_STATES" -gt 4 ]; then
    echo "✓ SUCCESS: Found $MCLK_STATES memory states (expected >4)"
    if [ "$MAX_MCLK" -ge 1800 ]; then
        echo "✓ SUCCESS: Maximum VRAM frequency is ${MAX_MCLK}MHz (target: 2000MHz)"
        echo "🎉 VRAM frequency issue has been RESOLVED!"
    else
        echo "⚠ PARTIAL: Maximum VRAM frequency is ${MAX_MCLK}MHz (target: 2000MHz)"
        echo "The fix improved the situation but didn't reach full speed."
    fi
else
    echo "✗ FAILED: Still only $MCLK_STATES memory states (expected >4)"
    echo "The kernel parameter fix didn't work. Try alternative solutions."
fi

echo ""
echo "=== Next Steps ==="
if [ "$MCLK_STATES" -le 4 ]; then
    echo "Since the kernel parameter didn't work, try these alternatives:"
    echo "1. Check BIOS settings for GPU memory configuration"
    echo "2. Try different ppfeaturemask values: 0xfff7ffff or 0xffffffff"
    echo "3. Consider VBIOS modification with MorePowerTool"
    echo "4. Update GPU VBIOS if available from manufacturer"
else
    echo "✓ Fix successful! For optimal performance:"
    echo "1. Set performance level to 'high' for gaming"
    echo "2. Monitor temperatures during heavy loads"
    echo "3. Use this script to track VRAM usage over time"
fi
