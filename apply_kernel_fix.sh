#!/bin/bash

# Apply kernel parameter fix for RX 6800 XT VRAM frequency
echo "=== Applying Kernel Parameter Fix ==="

# Backup current grub config
echo "1. Backing up GRUB configuration..."
sudo cp /etc/default/grub /etc/default/grub.backup.$(date +%Y%m%d_%H%M%S)

# Check current kernel parameters
echo ""
echo "2. Current kernel parameters:"
cat /proc/cmdline
echo ""

# Add the AMDGPU parameter
echo "3. Adding amdgpu.ppfeaturemask=0xffffffff to GRUB..."

# Check if parameter already exists
if grep -q "amdgpu.ppfeaturemask" /etc/default/grub; then
    echo "AMDGPU parameter already exists in GRUB config"
    grep "amdgpu.ppfeaturemask" /etc/default/grub
else
    # Add the parameter
    sudo sed -i 's/GRUB_CMDLINE_LINUX_DEFAULT="/&amdgpu.ppfeaturemask=0xffffffff /' /etc/default/grub
    echo "✓ Parameter added to GRUB configuration"
fi

echo ""
echo "4. Updated GRUB configuration:"
grep "GRUB_CMDLINE_LINUX_DEFAULT" /etc/default/grub

echo ""
echo "5. Updating GRUB..."
sudo grub-mkconfig -o /boot/grub/grub.cfg

echo ""
echo "=== Fix Applied Successfully ==="
echo ""
echo "IMPORTANT: You must REBOOT for this fix to take effect!"
echo ""
echo "After reboot, run this command to verify:"
echo "  cat /sys/class/drm/card0/device/pp_dpm_mclk"
echo ""
echo "You should see additional memory states like:"
echo "  0: 96Mhz"
echo "  1: 456Mhz" 
echo "  2: 673Mhz"
echo "  3: 1000Mhz"
echo "  4: 1500Mhz"
echo "  5: 1750Mhz"
echo "  6: 2000Mhz *"
echo ""
echo "Would you like to reboot now? (y/N)"
