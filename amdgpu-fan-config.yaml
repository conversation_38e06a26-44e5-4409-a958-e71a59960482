# Conservative AMD GPU Fan Configuration
# Reduces aggressive zero RPM cycling with very low minimum speeds

speed_matrix:
  # Format: [temperature(°C), speed(0-100%)]
  # Much more conservative curve - prioritizing silence over zero RPM
  - [0, 5]    # Very low minimum speed (~170 RPM instead of 0)
  - [35, 5]   # Keep very low at idle temps
  - [40, 8]   # Still very quiet (~270 RPM)
  - [45, 12]  # Gradual increase (~400 RPM)
  - [50, 18]  # Light gaming temps (~610 RPM)
  - [55, 20]  # Moderate temps
  - [60, 30]  # More noticeable cooling
  - [65, 35]  # Moderate cooling
  - [70, 40]  # Higher cooling
  - [75, 50]  # Aggressive cooling
  - [90, 75]  # Maximum cooling
  - [100, 100] # Emergency cooling

# Optional: Hysteresis to prevent rapid changes
# temp_drop_threshold: 3  # Temperature must drop 3°C before reducing fan speed

# Card selection (auto-detect first card)
cards:
  - card0

# Logging
log_level: INFO
