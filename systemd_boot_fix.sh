#!/bin/bash

# Fix for systemd-boot (not GRUB)
echo "=== Systemd-boot Configuration Fix ==="

# Find the current boot entry
BOOT_ENTRIES=$(ls /boot/loader/entries/*.conf 2>/dev/null)

if [ -z "$BOOT_ENTRIES" ]; then
    echo "No systemd-boot entries found. Checking alternative locations..."
    BOOT_ENTRIES=$(ls /efi/loader/entries/*.conf 2>/dev/null)
fi

if [ -z "$BOOT_ENTRIES" ]; then
    echo "Error: Could not find systemd-boot configuration files"
    echo "Please manually add 'amdgpu.ppfeaturemask=0xffffffff' to your kernel parameters"
    exit 1
fi

echo "Found boot entries:"
for entry in $BOOT_ENTRIES; do
    echo "  $entry"
done

# Get the most recent entry (likely the current kernel)
CURRENT_ENTRY=$(ls -t $BOOT_ENTRIES | head -1)
echo ""
echo "Using entry: $CURRENT_ENTRY"

# Backup the entry
sudo cp "$CURRENT_ENTRY" "${CURRENT_ENTRY}.backup.$(date +%Y%m%d_%H%M%S)"

echo ""
echo "Current configuration:"
cat "$CURRENT_ENTRY"

echo ""
echo "Adding AMDGPU parameter..."

# Check if parameter already exists
if grep -q "amdgpu.ppfeaturemask" "$CURRENT_ENTRY"; then
    echo "AMDGPU parameter already exists in boot entry"
else
    # Add the parameter to the options line
    sudo sed -i '/^options/ s/$/ amdgpu.ppfeaturemask=0xffffffff/' "$CURRENT_ENTRY"
    echo "✓ Parameter added to boot entry"
fi

echo ""
echo "Updated configuration:"
cat "$CURRENT_ENTRY"

echo ""
echo "=== Alternative Manual Method ==="
echo "If the automatic method didn't work, manually edit:"
echo "  $CURRENT_ENTRY"
echo ""
echo "Add 'amdgpu.ppfeaturemask=0xffffffff' to the 'options' line"
echo ""
echo "Example:"
echo "  options root=UUID=... rw amdgpu.ppfeaturemask=0xffffffff"
