#!/bin/bash

# Final attempt to force flash the VBIOS
echo "=== Final Force Flash Attempt ==="
echo ""
echo "CRITICAL DISCOVERY:"
echo "Your current VBIOS: 113-438MI-U89 (MI = Mining BIOS)"
echo "Target VBIOS:       113-438XT-U88 (XT = Gaming BIOS)"
echo ""
echo "This explains the VRAM limitation! You have a mining BIOS!"
echo ""

VBIOS_FILE="Sapphire.RX6800XT.16384.210112.rom"

# Try different approaches to force the flash
echo "Attempting various force flash methods..."

# Method 1: Try with different parameters
echo ""
echo "Method 1: Trying with -fa flag..."
sudo amdvbflash -fa -p 0 "$VBIOS_FILE" 2>/dev/null

# Method 2: Try to clear the flash first
echo ""
echo "Method 2: Trying to erase and reprogram..."
sudo amdvbflash -e 0 2>/dev/null && sudo amdvbflash -p 0 "$VBIOS_FILE" 2>/dev/null

# Method 3: Check if we can use different adapter addressing
echo ""
echo "Method 3: Trying different adapter specification..."
sudo amdvbflash -p 0000:03:00.0 "$VBIOS_FILE" 2>/dev/null

# Method 4: Try with subsystem override
echo ""
echo "Method 4: Trying with subsystem override..."
sudo amdvbflash -s 0 -p 0 "$VBIOS_FILE" 2>/dev/null

echo ""
echo "=== Flash Status Check ==="
sudo amdvbflash -i

echo ""
echo "=== Alternative Solutions ==="
echo ""
echo "If all flash attempts failed, here are your options:"
echo ""
echo "1. **Windows ATIFlash (Most Likely to Work)**"
echo "   - Boot to Windows"
echo "   - Download ATIFlash from TechPowerUp"
echo "   - Use: atiflash -f -p 0 Sapphire.RX6800XT.16384.210112.rom"
echo "   - Windows version has more override capabilities"
echo ""
echo "2. **Hardware BIOS Switch**"
echo "   - Check your GPU for a physical BIOS switch"
echo "   - Switch to secondary BIOS, flash primary"
echo "   - Switch back after successful flash"
echo ""
echo "3. **Different VBIOS File**"
echo "   - Try a newer VBIOS from TechPowerUp"
echo "   - Look for one with version > U89"
echo "   - Search for 'Sapphire RX 6800 XT Nitro+ Gaming'"
echo ""
echo "4. **Professional Service**"
echo "   - Some computer shops can flash GPU BIOS"
echo "   - They may have specialized tools"
echo ""
echo "5. **Contact Sapphire**"
echo "   - Request official VBIOS update"
echo "   - Mention you have mining BIOS on gaming card"
echo ""
echo "Your backup is safe at: original_vbios_backup_20250726_135327.rom"
