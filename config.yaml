# ============================================================================
# Beets Configuration File - Comprehensive Setup for Automated Music Library Management
# ============================================================================
# This configuration provides a hands-off, automated approach to music library
# management with intelligent defaults and comprehensive metadata handling.

# ============================================================================
# CORE LIBRARY SETTINGS
# ============================================================================

# Main music library directory - all organized music will be stored here
directory: /mnt/data/media/music

# Database location for beets library metadata
library: /opt/beets/music.db

# ============================================================================
# IMPORT SETTINGS - Core automation behavior
# ============================================================================
import:
  # Move files instead of copying (saves disk space, more efficient)
  move: yes
  
  # Write metadata tags to files during import
  write: yes
  
  # Don't copy files (move takes precedence)
  copy: no
  
  # Automatically apply strong matches without user intervention
  quiet: no
  
  # Default action for ambiguous matches (apply = accept best match)
  default_action: apply
  
  # Resume interrupted imports automatically
  resume: ask
  
  # Skip directories that have already been imported
  incremental: yes
  
  # Log import activity for troubleshooting
  log: /opt/beets/import.log
  
  # Group albums by metadata rather than directory structure
  group_albums: no
  
  # Enable automatic tagging (core feature)
  autotag: yes
  
  # Handle duplicates by asking user (can be changed to 'skip' for full automation)
  duplicate_action: skip
  
  # Show detailed duplicate information when found
  duplicate_verbose_prompt: yes
  
  # Fields used to detect duplicates
  duplicate_keys:
    album: albumartist album
    item: artist title
  
  # Ring bell when user input needed (useful for unattended imports)
  bell: yes
  
  # Set default fields for all imported music
  set_fields:
    comments: "Imported by beets"

# ============================================================================
# PLUGINS - Essential functionality for automated management
# ============================================================================
plugins:
  - fetchart      # Automatically download album artwork
  - embedart      # Embed artwork into audio files
  - duplicates    # Find and manage duplicate tracks
  - scrub         # Clean metadata and remove unwanted tags
  - lastgenre     # Fetch genre information from Last.fm
  - convert       # Audio format conversion capabilities
  - missing       # Find missing tracks in albums
  - info          # Display detailed track/album information
  - edit          # Edit metadata from command line
  - badfiles      # Check for corrupted audio files

# ============================================================================
# PATH ORGANIZATION - Intelligent file naming and directory structure
# ============================================================================
paths:
  # Default path for regular albums
  default: $albumartist/$album%aunique{}/$track $title
  
  # Path for compilation albums (Various Artists)
  comp: Compilations/$album%aunique{}/$track $title
  
  # Path for single tracks not part of an album
  singleton: Singles/$artist/$title
  
  # Special handling for soundtracks
  albumtype:soundtrack: Soundtracks/$album%aunique{}/$track $title
  
  # Special handling for live albums
  albumtype:live: Live/$albumartist/$album%aunique{}/$track $title
  
  # Special handling for classical music
  genre:classical: Classical/$albumartist/$album%aunique{}/$track $title

# ============================================================================
# FILENAME AND CHARACTER HANDLING
# ============================================================================

# Convert non-ASCII characters to ASCII equivalents for better compatibility
asciify_paths: yes

# Replace problematic characters in filenames
replace:
  '[\\/]': _           # Replace forward/back slashes
  '^\.': _             # Replace leading dots
  '[\x00-\x1f]': _     # Replace control characters
  '[<>:"\?\*\|]': _    # Replace Windows reserved characters
  '\.$': _             # Replace trailing dots
  '\s+$': ''           # Remove trailing whitespace
  '^\s+': ''           # Remove leading whitespace
  '^-': _              # Replace leading hyphens

# Album art filename (without extension)
art_filename: cover

# Maximum filename length (auto-detected by default)
max_filename_length: 200

# ============================================================================
# METADATA AND TAGGING SETTINGS
# ============================================================================

# Use ID3v2.3 for better compatibility with older software
id3v23: yes

# Use original release dates when available
original_date: no

# Use artist credits as they appear on releases
artist_credit: yes

# Per-disc track numbering for multi-disc releases
per_disc_numbering: no

# Various Artists album naming
va_name: 'Various Artists'

# Enable threaded operations for faster processing
threaded: yes

# ============================================================================
# MUSICBRAINZ CONFIGURATION
# ============================================================================
musicbrainz:
  # Enable MusicBrainz as metadata source
  enabled: yes
  
  # Number of search results to consider
  searchlimit: 10
  
  # Additional tags to include in searches for better matching
  extra_tags: [year, catalognum, country, media, label]
  
  # Use MusicBrainz genres
  genres: yes
  
  # Import external IDs for cross-referencing
  external_ids:
    discogs: yes
    spotify: yes
    bandcamp: yes

# ============================================================================
# AUTOTAGGER MATCHING CONFIGURATION
# ============================================================================
match:
  # Threshold for automatic acceptance (lower = more strict)
  strong_rec_thresh: 0.04
  
  # Medium recommendation threshold
  medium_rec_thresh: 0.25
  
  # Preferred countries and media types
  preferred:
    countries: ['US', 'GB', 'CA', 'AU']
    media: ['CD', 'Digital Media', 'Vinyl']
    original_year: yes
  
  # Required fields for matches
  required: [year]
  
  # Ignore these media types
  ignored_media: ['DVD', 'DVD-Video', 'Blu-ray', 'VHS', 'Data CD']
  
  # Ignore data and video tracks
  ignore_data_tracks: yes
  ignore_video_tracks: yes

# ============================================================================
# UI AND DISPLAY SETTINGS
# ============================================================================
ui:
  # Enable colored output
  color: yes
  
  # Terminal width for formatting
  terminal_width: 120
  
  # Length difference threshold for highlighting (seconds)
  length_diff_thresh: 10.0

# Display formats for listing
format_item: $artist - $album - $title
format_album: $albumartist - $album

# Default sorting
sort_item: artist+ album+ disc+ track+
sort_album: albumartist+ album+
sort_case_insensitive: yes

# ============================================================================
# PLUGIN CONFIGURATIONS
# ============================================================================

# FetchArt Plugin - Automatic album artwork downloading
fetchart:
  auto: yes                    # Automatically fetch art during import
  cautious: yes               # Only fetch if no art exists
  cover_names: cover folder   # Look for these filenames first
  sources: filesystem coverart itunes amazon albumart wikipedia google lyricwiki
  store_source: yes           # Remember where art came from

# EmbedArt Plugin - Embed artwork into audio files
embedart:
  auto: yes                   # Automatically embed during import
  ifempty: yes               # Only embed if no art exists in file
  maxwidth: 1000             # Maximum image width
  quality: 95                # JPEG quality for resizing

# Duplicates Plugin - Find duplicate tracks
duplicates:
  checksum: no               # Don't use expensive checksum comparison
  copy: no                   # Don't copy files when finding duplicates
  count: no                  # Don't just count, show details
  delete: no                 # Don't auto-delete (safety first)
  full: no                   # Don't compare file contents
  keys: [artist, title, album]  # Fields to compare for duplicates
  merge: no                  # Don't auto-merge duplicates
  move: no                   # Don't move duplicate files
  path: no                   # Don't compare file paths

# Scrub Plugin - Clean unwanted metadata
scrub:
  auto: yes                  # Automatically scrub during import

# LastGenre Plugin - Fetch genre information
lastgenre:
  auto: yes                  # Automatically fetch genres during import
  canonical: yes             # Use canonical genre names
  count: 3                   # Number of genres to fetch
  fallback: ''              # Fallback if no genre found
  force: no                  # Don't overwrite existing genres
  min_weight: 10            # Minimum weight for genre inclusion
  source: album             # Prefer album genres over track genres
  whitelist: yes            # Use genre whitelist for consistency

# Convert Plugin - Audio format conversion
convert:
  auto: no                   # Don't auto-convert during import
  dest: /mnt/data/media/music_converted
  format: mp3
  formats:
    mp3: 'ffmpeg -i $source -y -vn -acodec libmp3lame -ab 320k -ac 2 -ar 44100 $dest'
    flac: 'ffmpeg -i $source -y -vn -acodec flac $dest'

# Missing Plugin - Find missing tracks
missing:
  count: yes                 # Show count of missing tracks
  total: yes                 # Show total tracks in album



# ============================================================================
# LOGGING AND DEBUGGING
# ============================================================================

# Files to ignore during import
ignore: ['.*', '*~', 'System Volume Information', 'lost+found', 'Thumbs.db', '.DS_Store']

# Ignore hidden files
ignore_hidden: yes

# Clutter files that can be safely removed from empty directories
clutter: ['Thumbs.DB', '.DS_Store', '*.log', '*.txt', '*.nfo', '*.m3u', '*.pls']
