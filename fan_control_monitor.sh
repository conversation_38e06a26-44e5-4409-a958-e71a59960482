#!/bin/bash
# Fan Control Monitoring Script
# Real-time monitoring of intelligent fan control system

set -euo pipefail

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

print_header() {
    clear
    echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║              Intelligent Fan Control Monitor                 ║${NC}"
    echo -e "${CYAN}║                ASRock B660M-ITX/ac System                    ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo
}

get_service_status() {
    if systemctl is-active --quiet intelligent-fan-control; then
        echo -e "${GREEN}●${NC} RUNNING"
    else
        echo -e "${RED}●${NC} STOPPED"
    fi
}

get_fan_info() {
    local hwmon_path=""
    
    # Find NCT6798 hwmon path
    for hwmon in /sys/class/hwmon/hwmon*; do
        if [[ -f "$hwmon/name" ]] && grep -q "nct6798" "$hwmon/name" 2>/dev/null; then
            hwmon_path="$hwmon"
            break
        fi
    done
    
    if [[ -z "$hwmon_path" ]]; then
        echo "NCT6798 sensor not found"
        return
    fi
    
    echo -e "${BLUE}Fan Status:${NC}"
    for fan_num in 1 2 3 4 5 7; do
        local fan_file="$hwmon_path/fan${fan_num}_input"
        local pwm_file="$hwmon_path/pwm${fan_num}"
        local pwm_enable_file="$hwmon_path/pwm${fan_num}_enable"
        
        if [[ -f "$fan_file" ]]; then
            local rpm=$(cat "$fan_file" 2>/dev/null || echo "0")
            local pwm=""
            local mode=""
            
            if [[ -f "$pwm_file" ]]; then
                local pwm_val=$(cat "$pwm_file" 2>/dev/null || echo "0")
                local pwm_percent=$((pwm_val * 100 / 255))
                pwm=" (PWM: ${pwm_val}/255, ${pwm_percent}%)"
            fi
            
            if [[ -f "$pwm_enable_file" ]]; then
                local enable_val=$(cat "$pwm_enable_file" 2>/dev/null || echo "0")
                case $enable_val in
                    0) mode=" [FULL SPEED]" ;;
                    1) mode=" [MANUAL]" ;;
                    2) mode=" [AUTO]" ;;
                    *) mode=" [UNKNOWN]" ;;
                esac
            fi
            
            local fan_name=""
            case $fan_num in
                1) fan_name="CPU Fan     " ;;
                2) fan_name="Chassis Fan1" ;;
                3) fan_name="Chassis Fan2" ;;
                4) fan_name="Chassis Fan3" ;;
                5) fan_name="Chassis Fan4" ;;
                7) fan_name="Pump/Fan7   " ;;
            esac
            
            if [[ $rpm -gt 0 ]]; then
                echo -e "  ${fan_name}: ${GREEN}${rpm} RPM${NC}${pwm}${mode}"
            else
                echo -e "  ${fan_name}: ${YELLOW}0 RPM${NC}${pwm}${mode}"
            fi
        fi
    done
}

get_temperature_info() {
    echo -e "${BLUE}Temperature Status:${NC}"
    
    # CPU Temperature
    local cpu_temp=$(sensors coretemp-isa-0000 2>/dev/null | grep "Package id 0" | awk '{print $4}' | tr -d '+°C' || echo "N/A")
    if [[ "$cpu_temp" != "N/A" ]]; then
        if (( $(echo "$cpu_temp > 70" | bc -l 2>/dev/null || echo "0") )); then
            echo -e "  CPU Package: ${RED}${cpu_temp}°C${NC}"
        elif (( $(echo "$cpu_temp > 50" | bc -l 2>/dev/null || echo "0") )); then
            echo -e "  CPU Package: ${YELLOW}${cpu_temp}°C${NC}"
        else
            echo -e "  CPU Package: ${GREEN}${cpu_temp}°C${NC}"
        fi
    else
        echo -e "  CPU Package: ${RED}N/A${NC}"
    fi
    
    # GPU Temperature
    local gpu_temp=$(sensors amdgpu-pci-0300 2>/dev/null | grep "edge" | awk '{print $2}' | tr -d '+°C' || echo "N/A")
    if [[ "$gpu_temp" != "N/A" ]]; then
        if (( $(echo "$gpu_temp > 80" | bc -l 2>/dev/null || echo "0") )); then
            echo -e "  GPU Edge:    ${RED}${gpu_temp}°C${NC}"
        elif (( $(echo "$gpu_temp > 60" | bc -l 2>/dev/null || echo "0") )); then
            echo -e "  GPU Edge:    ${YELLOW}${gpu_temp}°C${NC}"
        else
            echo -e "  GPU Edge:    ${GREEN}${gpu_temp}°C${NC}"
        fi
    else
        echo -e "  GPU Edge:    ${YELLOW}N/A${NC}"
    fi
    
    # Additional CPU cores
    local core_temps=$(sensors coretemp-isa-0000 2>/dev/null | grep "Core" | awk '{print $3}' | tr -d '+°C' | head -3)
    if [[ -n "$core_temps" ]]; then
        echo -e "  CPU Cores:   ${CYAN}$(echo "$core_temps" | tr '\n' ' ')°C${NC}"
    fi
}

get_recent_logs() {
    echo -e "${BLUE}Recent Activity:${NC}"
    if systemctl is-active --quiet intelligent-fan-control; then
        journalctl -u intelligent-fan-control --no-pager -n 5 --output=cat 2>/dev/null | \
        while IFS= read -r line; do
            echo "  $line"
        done
    else
        echo -e "  ${YELLOW}Service not running${NC}"
    fi
}

show_controls() {
    echo
    echo -e "${CYAN}Controls:${NC}"
    echo "  [s] Start service    [t] Stop service     [r] Restart service"
    echo "  [l] View logs        [c] Clear screen     [q] Quit"
    echo "  [m] Manual test      [a] Auto refresh: $AUTO_REFRESH"
}

manual_test() {
    echo
    echo -e "${YELLOW}Starting 30-second manual test...${NC}"
    echo "Press Ctrl+C to stop early"
    echo
    
    sudo timeout 30 python3 /usr/local/bin/intelligent_fan_control.py 2 || {
        if [[ $? -eq 124 ]]; then
            echo -e "${GREEN}Manual test completed${NC}"
        else
            echo -e "${RED}Manual test failed${NC}"
        fi
    }
    
    echo
    read -p "Press Enter to continue..."
}

# Main monitoring loop
AUTO_REFRESH="ON"
REFRESH_INTERVAL=3

main() {
    while true; do
        print_header
        
        echo -e "${BLUE}Service Status:${NC} $(get_service_status)"
        echo
        
        get_temperature_info
        echo
        
        get_fan_info
        echo
        
        get_recent_logs
        
        show_controls
        
        if [[ "$AUTO_REFRESH" == "ON" ]]; then
            echo
            echo -e "${CYAN}Auto-refreshing in ${REFRESH_INTERVAL}s... (press any key for menu)${NC}"
            
            if read -t $REFRESH_INTERVAL -n 1 key; then
                case $key in
                    s|S)
                        sudo systemctl start intelligent-fan-control
                        ;;
                    t|T)
                        sudo systemctl stop intelligent-fan-control
                        ;;
                    r|R)
                        sudo systemctl restart intelligent-fan-control
                        ;;
                    l|L)
                        echo
                        echo "Recent logs (press 'q' to exit):"
                        sudo journalctl -u intelligent-fan-control -f
                        ;;
                    c|C)
                        clear
                        ;;
                    m|M)
                        manual_test
                        ;;
                    a|A)
                        if [[ "$AUTO_REFRESH" == "ON" ]]; then
                            AUTO_REFRESH="OFF"
                        else
                            AUTO_REFRESH="ON"
                        fi
                        ;;
                    q|Q)
                        echo
                        echo "Goodbye!"
                        exit 0
                        ;;
                esac
            fi
        else
            echo
            read -p "Enter command: " -n 1 key
            case $key in
                s|S)
                    sudo systemctl start intelligent-fan-control
                    ;;
                t|T)
                    sudo systemctl stop intelligent-fan-control
                    ;;
                r|R)
                    sudo systemctl restart intelligent-fan-control
                    ;;
                l|L)
                    echo
                    echo "Recent logs (press 'q' to exit):"
                    sudo journalctl -u intelligent-fan-control -f
                    ;;
                c|C)
                    clear
                    ;;
                m|M)
                    manual_test
                    ;;
                a|A)
                    if [[ "$AUTO_REFRESH" == "ON" ]]; then
                        AUTO_REFRESH="OFF"
                    else
                        AUTO_REFRESH="ON"
                    fi
                    ;;
                q|Q)
                    echo
                    echo "Goodbye!"
                    exit 0
                    ;;
            esac
        fi
    done
}

# Check dependencies
if ! command -v bc &> /dev/null; then
    echo "Installing bc for temperature calculations..."
    sudo pacman -S --noconfirm bc
fi

# Start monitoring
main
