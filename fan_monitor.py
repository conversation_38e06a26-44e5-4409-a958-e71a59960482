#!/usr/bin/env python3
"""
Fan Monitoring Script for ASRock B660M-ITX/ac + AMD GPU
Monitors all system fans for 10 minutes to identify intermittent fan behavior.

Usage: python3 fan_monitor.py [output_file.csv]
"""

import subprocess
import csv
import time
import sys
import re
from datetime import datetime
from statistics import mean
from collections import defaultdict

class FanMonitor:
    def __init__(self, output_file="fan_monitoring_data.csv"):
        self.output_file = output_file
        self.sample_count = 600  # 10 minutes at 1 sample/second
        self.sample_interval = 1.0  # seconds
        self.data = []
        self.fan_stats = defaultdict(list)
        
        # Define expected fans based on your system
        self.expected_fans = {
            'nct6798_fan1': 'MB_CPU_Fan',
            'nct6798_fan2': 'MB_Chassis_Fan1', 
            'nct6798_fan3': 'MB_Chassis_Fan2',
            'nct6798_fan4': 'MB_Chassis_Fan3',
            'nct6798_fan5': 'MB_Chassis_Fan4',
            'nct6798_fan7': 'MB_Pump_Fan',
            'amdgpu_fan1': 'GPU_Fan'
        }
        
    def get_sensor_data(self):
        """Parse sensors output and extract fan RPM data"""
        try:
            # Get raw sensor data
            result = subprocess.run(['sensors', '-u'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode != 0:
                raise Exception(f"sensors command failed: {result.stderr}")
            
            fan_data = {}
            current_chip = None
            
            for line in result.stdout.split('\n'):
                line = line.strip()
                
                # Identify sensor chips
                if 'nct6798' in line:
                    current_chip = 'nct6798'
                elif 'amdgpu' in line:
                    current_chip = 'amdgpu'
                
                # Parse fan input values
                if '_input:' in line and 'fan' in line:
                    match = re.search(r'fan(\d+)_input:\s+([\d.]+)', line)
                    if match and current_chip:
                        fan_num = match.group(1)
                        rpm_value = float(match.group(2))
                        fan_key = f"{current_chip}_fan{fan_num}"
                        fan_data[fan_key] = int(rpm_value)
            
            return fan_data
            
        except subprocess.TimeoutExpired:
            print("Warning: sensors command timed out")
            return {}
        except Exception as e:
            print(f"Error reading sensors: {e}")
            return {}
    
    def collect_sample(self):
        """Collect a single data sample"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        fan_data = self.get_sensor_data()
        
        # Create sample with timestamp and all expected fans
        sample = {'timestamp': timestamp}
        for fan_key, fan_name in self.expected_fans.items():
            rpm = fan_data.get(fan_key, 0)
            sample[fan_name] = rpm
            self.fan_stats[fan_name].append(rpm)
        
        return sample
    
    def write_csv_header(self):
        """Write CSV header"""
        fieldnames = ['timestamp'] + list(self.expected_fans.values())
        with open(self.output_file, 'w', newline='') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
    
    def append_to_csv(self, sample):
        """Append sample to CSV file"""
        fieldnames = ['timestamp'] + list(self.expected_fans.values())
        with open(self.output_file, 'a', newline='') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writerow(sample)
    
    def print_progress(self, current, total):
        """Print progress bar"""
        percent = (current / total) * 100
        bar_length = 40
        filled_length = int(bar_length * current // total)
        bar = '█' * filled_length + '-' * (bar_length - filled_length)
        
        print(f'\rCollecting data: [{bar}] {current}/{total} samples ({percent:.1f}%)', 
              end='', flush=True)
    
    def run_monitoring(self):
        """Main monitoring loop"""
        print(f"🔍 Fan Monitoring Script Started")
        print(f"📊 Collecting {self.sample_count} samples over 10 minutes")
        print(f"💾 Output file: {self.output_file}")
        print(f"⏱️  Sample interval: {self.sample_interval} seconds")
        print()
        
        # Test initial sensor read
        print("🧪 Testing sensor connectivity...")
        test_data = self.get_sensor_data()
        if not test_data:
            print("❌ Error: Cannot read sensor data. Please check lm-sensors installation.")
            return False
        
        print(f"✅ Found {len(test_data)} fan sensors:")
        for fan_key, rpm in test_data.items():
            fan_name = self.expected_fans.get(fan_key, fan_key)
            print(f"   {fan_name}: {rpm} RPM")
        print()
        
        # Initialize CSV file
        self.write_csv_header()
        
        # Start monitoring
        print("🚀 Starting data collection...")
        start_time = time.time()
        
        try:
            for i in range(self.sample_count):
                sample_start = time.time()
                
                # Collect sample
                sample = self.collect_sample()
                self.data.append(sample)
                self.append_to_csv(sample)
                
                # Update progress
                self.print_progress(i + 1, self.sample_count)
                
                # Sleep for remaining interval time
                elapsed = time.time() - sample_start
                sleep_time = max(0, self.sample_interval - elapsed)
                if sleep_time > 0:
                    time.sleep(sleep_time)
        
        except KeyboardInterrupt:
            print(f"\n\n⚠️  Monitoring interrupted by user after {len(self.data)} samples")
        
        total_time = time.time() - start_time
        print(f"\n\n✅ Data collection complete!")
        print(f"📈 Collected {len(self.data)} samples in {total_time:.1f} seconds")
        print(f"💾 Data saved to: {self.output_file}")
        
        return True
    
    def print_summary(self):
        """Print statistical summary of fan behavior"""
        if not self.fan_stats:
            print("❌ No data collected for summary")
            return
        
        print("\n" + "="*60)
        print("📊 FAN MONITORING SUMMARY")
        print("="*60)
        
        for fan_name, rpm_values in self.fan_stats.items():
            if not rpm_values:
                continue
                
            non_zero_values = [rpm for rpm in rpm_values if rpm > 0]
            
            print(f"\n🔧 {fan_name}:")
            print(f"   Total samples: {len(rpm_values)}")
            print(f"   Zero RPM samples: {len(rpm_values) - len(non_zero_values)}")
            
            if non_zero_values:
                min_rpm = min(non_zero_values)
                max_rpm = max(non_zero_values)
                avg_rpm = mean(non_zero_values)
                
                print(f"   Min RPM: {min_rpm}")
                print(f"   Max RPM: {max_rpm}")
                print(f"   Avg RPM: {avg_rpm:.1f}")
                print(f"   RPM Range: {max_rpm - min_rpm}")
                
                # Detect potential issues
                if max_rpm - min_rpm > 200:
                    print(f"   ⚠️  HIGH VARIATION DETECTED! Range: {max_rpm - min_rpm} RPM")
                
                # Count rapid changes
                rapid_changes = 0
                for i in range(1, len(rpm_values)):
                    if abs(rpm_values[i] - rpm_values[i-1]) > 100:
                        rapid_changes += 1
                
                if rapid_changes > 0:
                    print(f"   ⚠️  RAPID CHANGES: {rapid_changes} instances of >100 RPM change")
            else:
                print(f"   Status: Fan not active (0 RPM throughout monitoring)")

def main():
    output_file = sys.argv[1] if len(sys.argv) > 1 else "fan_monitoring_data.csv"
    
    monitor = FanMonitor(output_file)
    
    if monitor.run_monitoring():
        monitor.print_summary()
        
        print(f"\n🔍 ANALYSIS SUGGESTIONS:")
        print(f"1. Open {output_file} in a spreadsheet program")
        print(f"2. Create line graphs for each fan to visualize behavior")
        print(f"3. Look for sudden spikes or drops in RPM values")
        print(f"4. Check for fans with high variation or rapid changes")
        print(f"\n📈 Quick analysis commands:")
        print(f"   grep -v ',0,' {output_file} | head -10  # Show active fan samples")
        print(f"   awk -F',' '{{print $2}}' {output_file} | sort -n | uniq -c  # CPU fan RPM distribution")

if __name__ == "__main__":
    main()
