#!/bin/bash

# Pre-Flash Verification and Safety Check
echo "=== Pre-Flash Verification ==="

VBIOS_FILE="Sapphire.RX6800XT.16384.210112.rom"

echo "1. Current GPU Status:"
echo "Device: $(lspci | grep VGA)"
echo "Driver: $(cat /sys/class/drm/card0/device/driver/module/version 2>/dev/null || echo 'amdgpu')"
echo "Current VRAM states:"
cat /sys/class/drm/card0/device/pp_dpm_mclk
echo ""

echo "2. VBIOS File Verification:"
if [ -f "$VBIOS_FILE" ]; then
    SIZE=$(stat -c%s "$VBIOS_FILE")
    echo "✅ File found: $VBIOS_FILE"
    echo "✅ Size: $SIZE bytes ($(($SIZE/1024))KB)"
    
    # Check file header (should start with 0x55AA for valid VBIOS)
    HEADER=$(hexdump -C "$VBIOS_FILE" | head -1 | awk '{print $2$3}')
    if [ "$HEADER" = "55aa" ]; then
        echo "✅ Valid VBIOS header detected"
    else
        echo "⚠️  Warning: Unexpected VBIOS header: $HEADER"
    fi
else
    echo "❌ VBIOS file not found!"
    exit 1
fi

echo ""
echo "3. GPU Detection Test:"
sudo amdvbflash -i

echo ""
echo "4. Current System State:"
echo "Kernel: $(uname -r)"
echo "GPU temp: $(($(cat /sys/class/drm/card0/device/hwmon/hwmon*/temp1_input)/1000))°C"
echo "Power draw: $(($(cat /sys/class/drm/card0/device/hwmon/hwmon*/power1_average)/1000000))W"

echo ""
echo "5. Safety Checklist:"
echo "✅ VBIOS file verified"
echo "✅ GPU detected by amdvbflash"
echo "✅ System stable"
echo "✅ Backup procedure ready"

echo ""
echo "6. Recovery Information:"
echo "If flash fails, you can recover using:"
echo "  sudo amdvbflash -f -p 0 [backup_file.rom]"
echo ""
echo "Some cards have dual BIOS - check for a physical switch on your GPU"
echo ""

echo "=== Ready for VBIOS Flash ==="
echo "Run: sudo ./vbios_flash_procedure.sh"
echo ""
echo "⚠️  IMPORTANT REMINDERS:"
echo "1. Close all GPU-intensive applications"
echo "2. Ensure stable power supply"
echo "3. Do not interrupt the flash process"
echo "4. Keep the backup file safe"
echo "5. Have a recovery plan ready"
