#!/bin/bash

# Advanced VRAM Frequency Diagnostic and Fix Script
echo "=== Advanced VRAM Frequency Diagnostic ==="

# Check if we can access the power table for modification
echo "1. Checking power table access..."
if [ -r /sys/class/drm/card0/device/pp_table ]; then
    echo "✓ Power table accessible"
    PP_TABLE_SIZE=$(wc -c < /sys/class/drm/card0/device/pp_table)
    echo "  Power table size: $PP_TABLE_SIZE bytes"
else
    echo "✗ Power table not accessible"
fi

# Check VBIOS version
echo ""
echo "2. VBIOS Information..."
if [ -r /sys/class/drm/card0/device/vbios_version ]; then
    echo "VBIOS Version: $(cat /sys/class/drm/card0/device/vbios_version)"
else
    echo "VBIOS version not available"
fi

# Check memory type and configuration
echo ""
echo "3. Memory Configuration..."
echo "Total VRAM: $(($(cat /sys/class/drm/card0/device/mem_info_vram_total)/1024/1024/1024))GB"
echo "Used VRAM: $(($(cat /sys/class/drm/card0/device/mem_info_vram_used)/1024/1024))MB"

# Check if OverDrive is available
echo ""
echo "4. OverDrive Support..."
if [ -r /sys/class/drm/card0/device/pp_od_clk_voltage ]; then
    echo "✓ OverDrive available"
    cat /sys/class/drm/card0/device/pp_od_clk_voltage
else
    echo "✗ OverDrive not available (this may be the issue)"
fi

# Try to enable OverDrive
echo ""
echo "5. Attempting to enable OverDrive..."
sudo sh -c 'echo "1" > /sys/module/amdgpu/parameters/ppfeaturemask' 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✓ OverDrive parameter set"
else
    echo "✗ Cannot set OverDrive parameter (requires reboot with kernel parameter)"
fi

# Check current feature mask
echo ""
echo "6. Current AMDGPU feature mask..."
if [ -r /sys/module/amdgpu/parameters/ppfeaturemask ]; then
    FEATURE_MASK=$(cat /sys/module/amdgpu/parameters/ppfeaturemask)
    echo "Feature mask: $FEATURE_MASK"
    
    # Convert to binary to see enabled features
    if [ "$FEATURE_MASK" != "Y" ] && [ "$FEATURE_MASK" != "N" ]; then
        echo "Feature mask (hex): 0x$(printf '%x' $FEATURE_MASK)"
    fi
else
    echo "Feature mask not accessible"
fi

echo ""
echo "=== Recommended Solutions ==="
echo ""
echo "Based on diagnostics, your RX 6800 XT has a limited power table."
echo "Here are the solutions in order of effectiveness:"
echo ""
echo "SOLUTION 1: Add kernel parameter (RECOMMENDED)"
echo "Add this to your kernel command line:"
echo "  amdgpu.ppfeaturemask=0xffffffff"
echo ""
echo "SOLUTION 2: Use MorePowerTool (Windows-based VBIOS editor)"
echo "  - Boot to Windows"
echo "  - Use MorePowerTool to edit VBIOS memory timings"
echo "  - Add missing memory states (1500MHz, 1750MHz, 2000MHz)"
echo ""
echo "SOLUTION 3: Custom VBIOS (ADVANCED - RISKY)"
echo "  - Flash modified VBIOS with proper memory states"
echo "  - Requires backup and recovery plan"
echo ""
echo "SOLUTION 4: Force memory overclock (TEMPORARY)"
echo "  - Use manual memory overclocking tools"
echo "  - May not achieve full 2000MHz but can improve performance"
