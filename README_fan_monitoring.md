# Fan Monitoring Scripts for ASRock B660M-ITX/ac

## 🎯 Purpose
These scripts help identify intermittent fan behavior by monitoring all system fans for 10 minutes and logging their RPM values. Perfect for diagnosing fans that randomly spin up and down.

## 📁 Files Included
- `fan_monitor.py` - Python version with advanced features and analysis
- `fan_monitor.sh` - Bash version for systems without Python
- `README_fan_monitoring.md` - This usage guide

## 🔧 Detected Fans on Your System
Based on your ASRock B660M-ITX/ac motherboard and AMD GPU:

| Fan Name | Sensor ID | Description |
|----------|-----------|-------------|
| MB_CPU_Fan | nct6798_fan1 | CPU cooler fan |
| MB_Chassis_Fan1 | nct6798_fan2 | Chassis fan 1 (usually not connected) |
| MB_Chassis_Fan2 | nct6798_fan3 | Chassis fan 2 (usually not connected) |
| MB_Chassis_Fan3 | nct6798_fan4 | Chassis fan 3 (usually not connected) |
| MB_Chassis_Fan4 | nct6798_fan5 | Chassis fan 4 (usually not connected) |
| MB_Pump_Fan | nct6798_fan7 | Water pump or chassis fan |
| GPU_Fan | amdgpu_fan1 | AMD RX 6800/6800 XT/6900 XT fan |

## 🚀 Quick Start

### Option 1: Python Script (Recommended)
```bash
# Run with default filename
./fan_monitor.py

# Run with custom filename
./fan_monitor.py my_fan_data.csv
```

### Option 2: Bash Script
```bash
# Run with default filename
./fan_monitor.sh

# Run with custom filename
./fan_monitor.sh my_fan_data.csv
```

## 📊 What the Scripts Do

1. **Data Collection**: Samples all fan RPMs once per second for 600 seconds (10 minutes)
2. **Real-time Progress**: Shows progress bar during collection
3. **CSV Output**: Saves timestamped data to CSV file
4. **Analysis**: Provides summary statistics and identifies potential issues
5. **Error Handling**: Gracefully handles sensor read failures

## 📈 Sample Output

### During Collection:
```
🔍 Fan Monitoring Script Started
📊 Collecting 600 samples over 10 minutes
💾 Output file: fan_monitoring_data.csv
⏱️  Sample interval: 1.0 seconds

🧪 Testing sensor connectivity...
✅ Found 7 fan sensors:
   MB_CPU_Fan: 617 RPM
   MB_Chassis_Fan1: 0 RPM
   MB_Chassis_Fan2: 0 RPM
   MB_Chassis_Fan3: 0 RPM
   MB_Chassis_Fan4: 0 RPM
   MB_Pump_Fan: 632 RPM
   GPU_Fan: 598 RPM

🚀 Starting data collection...
Collecting data: [████████████████████████████████████████] 600/600 samples (100.0%)

✅ Data collection complete!
📈 Collected 600 samples in 600.1 seconds
💾 Data saved to: fan_monitoring_data.csv
```

### Analysis Summary:
```
================================================================
📊 FAN MONITORING SUMMARY
================================================================

🔧 MB_CPU_Fan:
   Total samples: 600
   Zero RPM samples: 0
   Min RPM: 615
   Max RPM: 620
   Avg RPM: 617.3
   RPM Range: 5

🔧 MB_Pump_Fan:
   Total samples: 600
   Zero RPM samples: 0
   Min RPM: 630
   Max RPM: 635
   Avg RPM: 632.1
   RPM Range: 5

🔧 GPU_Fan:
   Total samples: 600
   Zero RPM samples: 0
   Min RPM: 595
   Max RPM: 1200
   Avg RPM: 650.2
   RPM Range: 605
   ⚠️  HIGH VARIATION DETECTED! Range: 605 RPM
   ⚠️  RAPID CHANGES: 15 instances of >100 RPM change
```

## 🔍 Identifying Problem Fans

### Warning Signs to Look For:
1. **High RPM Range**: Difference between min/max > 200 RPM
2. **Rapid Changes**: Frequent jumps > 100 RPM between samples
3. **Intermittent Zero RPM**: Fan stopping and starting
4. **Erratic Patterns**: Irregular speed variations

### Analysis Commands:
```bash
# View the CSV data
head -20 fan_monitoring_data.csv

# Find samples with high CPU fan speeds
awk -F',' '$2 > 700 {print}' fan_monitoring_data.csv

# Check for rapid GPU fan changes
awk -F',' 'NR>1 {if(prev && ($8-prev > 100 || prev-$8 > 100)) print $1","prev","$8} {prev=$8}' fan_monitoring_data.csv

# Count zero RPM samples for each fan
for i in {2..8}; do
    echo "Column $i zeros: $(cut -d',' -f$i fan_monitoring_data.csv | grep -c '^0$')"
done
```

## 📊 Data Visualization

### Import into Spreadsheet:
1. Open LibreOffice Calc or Excel
2. Import the CSV file
3. Create line charts for each fan column
4. Look for:
   - Sudden spikes or drops
   - Regular patterns vs irregular behavior
   - Fans that should be running but show 0 RPM

### Command Line Plotting (if you have gnuplot):
```bash
# Plot CPU fan over time
gnuplot -e "
set datafile separator ',';
set xdata time;
set timefmt '%Y-%m-%d %H:%M:%S';
set format x '%H:%M';
plot 'fan_monitoring_data.csv' using 1:2 with lines title 'CPU Fan RPM'
"
```

## 🛠️ Troubleshooting

### Script Won't Run:
```bash
# Make executable
chmod +x fan_monitor.py fan_monitor.sh

# Check sensors installation
sensors
sudo sensors-detect --auto
```

### No Fan Data:
```bash
# Check if sensors are detected
sensors -u | grep fan

# Verify motherboard sensor chip
sensors | grep nct6798
```

### Permission Issues:
```bash
# Run with sudo if needed (usually not required)
sudo ./fan_monitor.py
```

## 🎯 Next Steps After Monitoring

1. **Analyze the CSV data** to identify which fan shows problematic behavior
2. **Check physical connections** for fans with erratic readings
3. **Inspect BIOS fan curves** for fans with high variation
4. **Consider fan replacement** for fans with consistent issues
5. **Monitor temperatures** during fan irregularities

## 📝 Notes

- The scripts automatically handle sensor read failures
- Data is saved continuously, so you can interrupt and still have partial data
- Both scripts produce identical CSV output format
- The Python version has more detailed analysis features
- Normal fan speed variations of ±50 RPM are typically acceptable

## 🆘 Support

If you encounter issues:
1. Check that `lm-sensors` is installed and configured
2. Verify sensors work: `sensors`
3. Run a short test: modify `SAMPLE_COUNT=10` in the script
4. Check file permissions and disk space

The scripts are designed to be robust and handle most common sensor issues automatically.
