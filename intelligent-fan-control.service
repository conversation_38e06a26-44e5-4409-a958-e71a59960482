[Unit]
Description=Intelligent Case Fan Control System
Documentation=https://wiki.archlinux.org/title/Fan_speed_control
After=multi-user.target
Wants=lm_sensors.service

[Service]
Type=simple
ExecStart=/usr/bin/python3 /usr/local/bin/intelligent_fan_control.py 5
ExecStop=/bin/kill -TERM $MAINPID
Restart=on-failure
RestartSec=10
User=root
Group=root

# Security settings
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/sys/class/hwmon /var/log
PrivateTmp=true
ProtectKernelTunables=false
ProtectKernelModules=true
ProtectControlGroups=true

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=intelligent-fan-control

[Install]
WantedBy=multi-user.target
