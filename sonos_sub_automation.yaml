# Sonos Sub Dynamic Group Management Automation
# This automation dynamically moves the Sonos Sub between two speaker groups
# based on playback activity.

# PREREQUISITES:
# 1. Replace entity_id values with your actual Sonos speaker entity names
# 2. Ensure all Sonos speakers are properly configured in Home Assistant
# 3. Test the automation in a safe environment first

# ENTITY NAMING CONVENTION (Update these with your actual entity names):
# - media_player.surround: Main speaker in your 5.1 surround system
# - media_player.sonos_sub: Your Sonos Sub
# - media_player.unnamed_room: Left speaker in your stereo pair
# - media_player.sonos_stereo_right: Right speaker in your stereo pair (skip for now)

automation:
  # Automation 1: Move Sub to Stereo Group when stereo starts playing
  - id: sonos_sub_to_stereo
    alias: "Sonos Sub: Move to Stereo Group"
    description: "Move Sonos Sub from surround to stereo group when stereo starts playing"
    
    trigger:
      # Trigger when either stereo speaker starts playing
      - platform: state
        entity_id:
          - media_player.unnamed_room
          - media_player.sonos_stereo_right
        to: "playing"
        for:
          seconds: 2  # Small delay to avoid false triggers
    
    condition:
      - condition: and
        conditions:
          # Ensure the stereo group is actually playing music
          - condition: or
            conditions:
              - condition: state
                entity_id: media_player.unnamed_room
                state: "playing"
              - condition: state
                entity_id: media_player.sonos_stereo_right
                state: "playing"
          
          # Ensure Sub is not already in the stereo group
          - condition: template
            value_template: >
              {% set sub_group = state_attr('media_player.sonos_sub', 'group_members') %}
              {% set stereo_group = state_attr('media_player.unnamed_room', 'group_members') %}
              {{ sub_group != stereo_group }}
          
          # Ensure surround system is not actively playing
          - condition: not
            conditions:
              - condition: state
                entity_id: media_player.surround
                state: "playing"
    
    action:
      - service: media_player.unjoin
        target:
          entity_id: media_player.sonos_sub
        
      - delay:
          seconds: 1
        
      - service: media_player.join
        target:
          entity_id: media_player.unnamed_room
        data:
          group_members:
            - media_player.sonos_sub
    
    mode: single

  # Automation 2: Move Sub back to Surround Group when stereo stops playing
  - id: sonos_sub_to_surround
    alias: "Sonos Sub: Return to Surround Group"
    description: "Move Sonos Sub back to surround group when stereo stops playing"
    
    trigger:
      # Trigger when stereo speakers stop playing
      - platform: state
        entity_id:
          - media_player.unnamed_room
          - media_player.sonos_stereo_right
        from: "playing"
        to:
          - "idle"
          - "paused"
          - "off"
        for:
          seconds: 30  # Wait 30 seconds to avoid quick switches
    
    condition:
      - condition: and
        conditions:
          # Ensure both stereo speakers are not playing
          - condition: not
            conditions:
              - condition: state
                entity_id: media_player.unnamed_room
                state: "playing"
          - condition: not
            conditions:
              - condition: state
                entity_id: media_player.sonos_stereo_right
                state: "playing"
          
          # Ensure Sub is currently in the stereo group
          - condition: template
            value_template: >
              {% set sub_group = state_attr('media_player.sonos_sub', 'group_members') %}
              {% set stereo_group = state_attr('media_player.unnamed_room', 'group_members') %}
              {{ sub_group == stereo_group }}
    
    action:
      - service: media_player.unjoin
        target:
          entity_id: media_player.sonos_sub
        
      - delay:
          seconds: 1
        
      - service: media_player.join
        target:
          entity_id: media_player.surround
        data:
          group_members:
            - media_player.sonos_sub
    
    mode: single

  # Automation 3: Priority override - Move Sub to Surround when surround starts playing
  - id: sonos_sub_surround_priority
    alias: "Sonos Sub: Surround Priority Override"
    description: "Move Sub to surround group when surround system starts playing (priority override)"
    
    trigger:
      - platform: state
        entity_id: media_player.surround
        to: "playing"
        for:
          seconds: 2
    
    condition:
      # Only act if Sub is not already in the surround group
      - condition: template
        value_template: >
          {% set sub_group = state_attr('media_player.sonos_sub', 'group_members') %}
          {% set surround_group = state_attr('media_player.surround', 'group_members') %}
          {{ sub_group != surround_group }}
    
    action:
      - service: media_player.unjoin
        target:
          entity_id: media_player.sonos_sub
        
      - delay:
          seconds: 1
        
      - service: media_player.join
        target:
          entity_id: media_player.surround
        data:
          group_members:
            - media_player.sonos_sub
    
    mode: single

# Optional: Script for manual Sub management
script:
  sonos_sub_to_stereo_manual:
    alias: "Manually Move Sub to Stereo"
    sequence:
      - service: media_player.unjoin
        target:
          entity_id: media_player.sonos_sub
      - delay:
          seconds: 1
      - service: media_player.join
        target:
          entity_id: media_player.unnamed_room
        data:
          group_members:
            - media_player.sonos_sub

  sonos_sub_to_surround_manual:
    alias: "Manually Move Sub to Surround"
    sequence:
      - service: media_player.unjoin
        target:
          entity_id: media_player.sonos_sub
      - delay:
          seconds: 1
      - service: media_player.join
        target:
          entity_id: media_player.surround
        data:
          group_members:
            - media_player.sonos_sub
