#!/bin/bash

# GPU Stress Test to Verify VRAM Frequency Scaling
echo "=== GPU Stress Test for VRAM Frequency Verification ==="

# Check if stress testing tools are available
if ! command -v glxgears &> /dev/null && ! command -v vkcube &> /dev/null; then
    echo "Installing basic GPU stress testing tools..."
    sudo pacman -S --noconfirm mesa-utils vulkan-tools 2>/dev/null || echo "Please install mesa-utils and vulkan-tools manually"
fi

echo "Starting GPU stress test to trigger VRAM frequency scaling..."
echo "Monitor VRAM frequency in another terminal with: watch -n 1 cat /sys/class/drm/card0/device/pp_dpm_mclk"
echo ""

# Function to monitor GPU stats during stress test
monitor_gpu() {
    echo "=== GPU Stats During Load ==="
    echo "VRAM States:"
    cat /sys/class/drm/card0/device/pp_dpm_mclk
    echo ""
    echo "GPU Temperature: $(($(cat /sys/class/drm/card0/device/hwmon/hwmon*/temp1_input)/1000))°C"
    echo "VRAM Temperature: $(($(cat /sys/class/drm/card0/device/hwmon/hwmon*/temp2_input)/1000))°C"
    echo "Power Draw: $(($(cat /sys/class/drm/card0/device/hwmon/hwmon*/power1_average)/1000000))W"
    echo "Used VRAM: $(($(cat /sys/class/drm/card0/device/mem_info_vram_used)/1024/1024))MB"
    echo ""
}

# Set high performance mode for testing
echo "Setting high performance mode..."
sudo sh -c 'echo "high" > /sys/class/drm/card0/device/power_dpm_force_performance_level'

echo "Baseline stats (idle):"
monitor_gpu

echo "Starting stress test..."
echo "Press Ctrl+C to stop the test"

# Run a simple GPU stress test
if command -v vkcube &> /dev/null; then
    echo "Running Vulkan cube demo (close window to stop)..."
    vkcube &
    STRESS_PID=$!
elif command -v glxgears &> /dev/null; then
    echo "Running OpenGL gears (close window to stop)..."
    glxgears &
    STRESS_PID=$!
else
    echo "No GPU stress test available. Please run a GPU-intensive application manually."
    echo "Suggested applications: games, Blender, or GPU benchmarks"
    STRESS_PID=""
fi

# Monitor for 30 seconds
for i in {1..6}; do
    sleep 5
    echo "=== Measurement $i/6 ($(($i * 5)) seconds) ==="
    monitor_gpu
done

# Clean up
if [ ! -z "$STRESS_PID" ]; then
    kill $STRESS_PID 2>/dev/null
fi

echo "=== Stress Test Complete ==="
echo "Reset to auto performance mode..."
sudo sh -c 'echo "auto" > /sys/class/drm/card0/device/power_dpm_force_performance_level'

echo ""
echo "Final stats (after load):"
monitor_gpu

echo "=== Test Results ==="
echo "Check if VRAM frequency reached higher states during the test."
echo "Expected behavior: VRAM should scale from 1000MHz to 1500-2000MHz under load."
