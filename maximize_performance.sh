#!/bin/bash

# Maximize RX 6800 XT Performance with Limited VBIOS
# This script works around the VBIOS limitation to get best possible performance

echo "=== Maximizing RX 6800 XT Performance ==="
echo "Working around VBIOS memory limitation..."

# Set manual performance mode
echo "Setting manual performance mode..."
sudo sh -c 'echo "manual" > /sys/class/drm/card0/device/power_dpm_force_performance_level'

# Overclock memory to maximum allowed (1075MHz)
echo "Overclocking VRAM to maximum allowed (1075MHz)..."
sudo sh -c 'echo "m 1 1075" > /sys/class/drm/card0/device/pp_od_clk_voltage'
sudo sh -c 'echo "c" > /sys/class/drm/card0/device/pp_od_clk_voltage'

# Force highest memory state
echo "Forcing highest memory state..."
sudo sh -c 'echo "3" > /sys/class/drm/card0/device/pp_dpm_mclk'

# Maximize GPU clocks too
echo "Maximizing GPU clocks..."
sudo sh -c 'echo "7" > /sys/class/drm/card0/device/pp_dpm_sclk'

# Set aggressive power profile
echo "Setting COMPUTE power profile for maximum performance..."
sudo sh -c 'echo "5" > /sys/class/drm/card0/device/pp_power_profile_mode'

echo ""
echo "=== Performance Maximized ==="
echo "Current settings:"
echo "VRAM frequency: $(cat /sys/class/drm/card0/device/pp_dpm_mclk | grep '*' | awk '{print $2}')"
echo "GPU frequency: $(cat /sys/class/drm/card0/device/pp_dpm_sclk | grep '*' | awk '{print $2}')"
echo "Power profile: $(cat /sys/class/drm/card0/device/pp_power_profile_mode | grep '*' | awk '{print $1 $2}')"
echo ""
echo "⚠ Note: This is still limited by your VBIOS to ~1075MHz instead of 2000MHz"
echo "For full performance, you need to modify your VBIOS (see vbios_solution_guide.md)"

# Create a service to apply these settings on boot
echo ""
echo "Would you like to create a systemd service to apply these settings on boot? (y/N)"
read -r response
if [[ "$response" =~ ^[Yy]$ ]]; then
    sudo tee /etc/systemd/system/amdgpu-performance.service > /dev/null << 'EOF'
[Unit]
Description=AMD GPU Performance Optimization
After=graphical.target

[Service]
Type=oneshot
ExecStart=/home/<USER>/scripts/maximize_performance.sh
RemainAfterExit=yes

[Install]
WantedBy=graphical.target
EOF

    sudo systemctl enable amdgpu-performance.service
    echo "✓ Service created and enabled. Settings will apply on boot."
fi
