#!/bin/bash

# Patch VBIOS SSID to match your card
echo "=== VBIOS SSID Patching ==="

ORIGINAL_VBIOS="267602.rom"
PATCHED_VBIOS="267602_patched_438E.rom"

echo "Creating SSID-patched VBIOS..."
echo "Original SSID: E438"
echo "Target SSID:   438E (your card)"
echo ""

# Copy original to patched version
cp "$ORIGINAL_VBIOS" "$PATCHED_VBIOS"

# Find and replace SSID in the VBIOS
# SSID is stored in little-endian format in the VBIOS
echo "Patching SSID from E438 to 438E..."

# Use hexdump to find the location and sed to replace
# E438 in little-endian is 38 E4, we need to change it to 8E 43
python3 -c "
import sys
with open('$PATCHED_VBIOS', 'rb') as f:
    data = bytearray(f.read())

# Look for the SSID pattern (E438 = 0x38E4 in little-endian)
old_ssid = b'\x38\xE4'
new_ssid = b'\x8E\x43'  # 438E in little-endian

count = 0
for i in range(len(data) - 1):
    if data[i:i+2] == old_ssid:
        data[i:i+2] = new_ssid
        count += 1
        print(f'Replaced SSID at offset 0x{i:04X}')

if count > 0:
    with open('$PATCHED_VBIOS', 'wb') as f:
        f.write(data)
    print(f'Successfully patched {count} SSID occurrences')
else:
    print('No SSID patterns found to patch')
    sys.exit(1)
"

if [ $? -ne 0 ]; then
    echo "❌ SSID patching failed"
    exit 1
fi

echo ""
echo "✅ SSID patched successfully"
echo "Patched VBIOS: $PATCHED_VBIOS"

# Verify the patch
echo ""
echo "Verifying patch..."
if hexdump -C "$PATCHED_VBIOS" | grep -q "8e 43"; then
    echo "✅ New SSID (438E) found in patched VBIOS"
else
    echo "⚠️  Warning: Could not verify SSID patch"
fi

echo ""
echo "Now attempting to flash patched VBIOS..."
echo "⚠️  This is experimental - ensure you have backups!"
echo ""
echo "Type 'PATCH' to proceed with flashing the patched VBIOS:"
read -r confirmation

if [ "$confirmation" != "PATCH" ]; then
    echo "❌ Aborted by user"
    echo "Patched VBIOS saved as: $PATCHED_VBIOS"
    echo "You can try flashing it later with: sudo amdvbflash -p 0 $PATCHED_VBIOS"
    exit 1
fi

# Attempt to flash the patched VBIOS
echo ""
echo "🔥 FLASHING PATCHED VBIOS..."
sudo amdvbflash -p 0 "$PATCHED_VBIOS"

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 FLASH SUCCESSFUL!"
    echo ""
    echo "Verifying new VBIOS..."
    sudo amdvbflash -i
    echo ""
    echo "⚠️  REBOOT REQUIRED to activate new VBIOS"
    echo "After reboot, check: cat /sys/class/drm/card0/device/pp_dpm_mclk"
else
    echo ""
    echo "❌ Flash failed even with SSID patch"
    echo "The external amdvbflash tool has strong protections"
    echo ""
    echo "Final recommendation: Use Windows ATIFlash with -f flag"
    echo "Command: atiflash -f -p 0 $PATCHED_VBIOS"
fi
