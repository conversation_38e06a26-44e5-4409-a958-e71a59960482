#!/bin/bash
# GPU Fan Speed Testing Script
# Test different fan speeds to find the sweet spot

set -euo pipefail

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${GREEN}✅${NC} $1"; }
print_warning() { echo -e "${YELLOW}⚠️${NC} $1"; }
print_error() { echo -e "${RED}❌${NC} $1"; }
print_info() { echo -e "${BLUE}ℹ️${NC} $1"; }

# Find AMD GPU hwmon device
find_amd_hwmon() {
    for hwmon in /sys/class/hwmon/hwmon*; do
        if [[ -f "$hwmon/name" ]] && grep -q "amdgpu" "$hwmon/name" 2>/dev/null; then
            echo "$hwmon"
            return 0
        fi
    done
    return 1
}

# Get current fan info
get_fan_info() {
    local hwmon_path="$1"
    local temp_millidegrees=$(cat "$hwmon_path/temp1_input" 2>/dev/null || echo "0")
    local temp_celsius=$((temp_millidegrees / 1000))
    local pwm_value=$(cat "$hwmon_path/pwm1" 2>/dev/null || echo "0")
    local pwm_percent=$((pwm_value * 100 / 255))
    
    echo "Temperature: ${temp_celsius}°C, PWM: ${pwm_value}/255 (${pwm_percent}%)"
}

# Set fan speed and show result
test_fan_speed() {
    local hwmon_path="$1"
    local pwm_value="$2"
    local description="$3"
    
    echo "$pwm_value" | sudo tee "$hwmon_path/pwm1" > /dev/null
    sleep 10  # Wait for fan to adjust
    
    local actual_rpm=$(sensors amdgpu-pci-0300 | grep "fan1:" | awk '{print $2}' | tr -d ' ')
    local pwm_percent=$((pwm_value * 100 / 255))
    
    print_info "$description: PWM $pwm_value/255 (${pwm_percent}%) = $actual_rpm"
}

# Restore automatic control
restore_auto() {
    local hwmon_path="$1"
    echo 2 | sudo tee "$hwmon_path/pwm1_enable" > /dev/null
    print_status "Automatic fan control restored"
}

main() {
    echo "🧪 GPU Fan Speed Testing"
    echo "======================="
    
    # Find AMD GPU
    if ! HWMON_PATH=$(find_amd_hwmon); then
        print_error "AMD GPU hwmon device not found"
        exit 1
    fi
    
    print_status "Found AMD GPU at: $HWMON_PATH"
    
    # Show current status
    print_info "Current status: $(get_fan_info "$HWMON_PATH")"
    
    # Enable manual control
    echo 1 | sudo tee "$HWMON_PATH/pwm1_enable" > /dev/null
    print_status "Manual fan control enabled"
    
    echo
    print_warning "Testing different fan speeds..."
    echo "Press Ctrl+C at any time to restore automatic control"
    echo
    
    # Test very low speeds
    test_fan_speed "$HWMON_PATH" "13" "Very Low (5%)"
    test_fan_speed "$HWMON_PATH" "20" "Low (8%)"
    test_fan_speed "$HWMON_PATH" "26" "Quiet (10%)"
    test_fan_speed "$HWMON_PATH" "31" "Minimal (12%)"
    test_fan_speed "$HWMON_PATH" "38" "Conservative (15%)"
    
    echo
    print_info "Based on your preference, choose a PWM value:"
    echo "  13 (5%) = ~170 RPM - Nearly silent"
    echo "  20 (8%) = ~270 RPM - Very quiet"
    echo "  26 (10%) = ~350 RPM - Quiet"
    echo "  31 (12%) = ~420 RPM - Minimal audible"
    echo "  38 (15%) = ~510 RPM - Conservative"
    
    echo
    read -p "Enter your preferred PWM value (13-38) or press Enter to restore auto: " choice
    
    if [[ -n "$choice" && "$choice" =~ ^[0-9]+$ && "$choice" -ge 13 && "$choice" -le 38 ]]; then
        echo "$choice" | sudo tee "$HWMON_PATH/pwm1" > /dev/null
        print_status "Fan speed set to PWM $choice"
        print_warning "Manual control is active. To restore auto control later:"
        echo "  echo 2 | sudo tee $HWMON_PATH/pwm1_enable"
    else
        restore_auto "$HWMON_PATH"
    fi
}

# Handle Ctrl+C
trap 'echo; print_warning "Restoring automatic control..."; restore_auto "$HWMON_PATH"; exit 0' INT

main "$@"
