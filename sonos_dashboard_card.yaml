# Sonos Sub Management Dashboard Card
# Add this to your Home Assistant dashboard for manual control

# Card 1: Sonos Sub Control Card
type: vertical-stack
cards:
  - type: markdown
    content: |
      ## 🔊 Sonos Sub Management
      Current Sub Location: **{{ state_attr('media_player.sonos_sub', 'group_members')[0] | replace('media_player.sonos_', '') | title }}**
  
  - type: entities
    title: Manual Controls
    entities:
      - entity: script.sonos_sub_to_stereo_manual
        name: "Move Sub to Stereo"
        icon: mdi:speaker-multiple
      - entity: script.sonos_sub_to_surround_manual
        name: "Move Sub to Surround"
        icon: mdi:surround-sound
      - type: divider
      - entity: automation.sonos_sub_to_stereo
        name: "Auto: Sub to Stereo"
      - entity: automation.sonos_sub_to_surround
        name: "Auto: Sub to Surround"
      - entity: automation.sonos_sub_surround_priority
        name: "Auto: Surround Priority"

  - type: entities
    title: Speaker Status
    entities:
      - entity: media_player.sonos_surround_main
        name: "Surround Main"
      - entity: media_player.sonos_sub
        name: "Sonos Sub"
      - entity: media_player.sonos_stereo_left
        name: "Stereo Left"
      - entity: media_player.sonos_stereo_right
        name: "Stereo Right"

# Card 2: Compact Button Card (Alternative)
---
type: horizontal-stack
cards:
  - type: button
    tap_action:
      action: call-service
      service: script.sonos_sub_to_stereo_manual
    name: "Sub → Stereo"
    icon: mdi:speaker-multiple
    show_state: false
  
  - type: button
    tap_action:
      action: call-service
      service: script.sonos_sub_to_surround_manual
    name: "Sub → Surround"
    icon: mdi:surround-sound
    show_state: false

# Card 3: Advanced Status Card with Conditional Formatting
---
type: custom:mushroom-template-card
primary: |
  Sonos Sub: {{ state_attr('media_player.sonos_sub', 'group_members')[0] | replace('media_player.sonos_', '') | title }}
secondary: |
  {% set sub_state = states('media_player.sonos_sub') %}
  {% if sub_state == 'playing' %}
    🎵 Playing
  {% elif sub_state == 'paused' %}
    ⏸️ Paused
  {% elif sub_state == 'idle' %}
    💤 Idle
  {% else %}
    ❓ {{ sub_state | title }}
  {% endif %}
icon: |
  {% set sub_group = state_attr('media_player.sonos_sub', 'group_members') %}
  {% set stereo_group = state_attr('media_player.sonos_stereo_left', 'group_members') %}
  {% if sub_group == stereo_group %}
    mdi:speaker-multiple
  {% else %}
    mdi:surround-sound
  {% endif %}
icon_color: |
  {% set sub_state = states('media_player.sonos_sub') %}
  {% if sub_state == 'playing' %}
    green
  {% elif sub_state == 'paused' %}
    orange
  {% else %}
    grey
  {% endif %}
tap_action:
  action: more-info
  entity: media_player.sonos_sub

# Card 4: Group Visualization Card
---
type: markdown
content: |
  ## 🎵 Current Sonos Groups
  
  **Surround Group:**
  {% set surround_group = state_attr('media_player.sonos_surround_main', 'group_members') %}
  {% for member in surround_group %}
  - {{ member | replace('media_player.sonos_', '') | title }} ({{ states(member) }})
  {% endfor %}
  
  **Stereo Group:**
  {% set stereo_group = state_attr('media_player.sonos_stereo_left', 'group_members') %}
  {% for member in stereo_group %}
  - {{ member | replace('media_player.sonos_', '') | title }} ({{ states(member) }})
  {% endfor %}

# Card 5: Automation Status Indicators
---
type: glance
title: Automation Status
entities:
  - entity: automation.sonos_sub_to_stereo
    name: "→ Stereo"
  - entity: automation.sonos_sub_to_surround
    name: "→ Surround"
  - entity: automation.sonos_sub_surround_priority
    name: "Priority"
columns: 3
show_name: true
show_state: true

# Instructions for use:
# 1. Copy any of the above card configurations
# 2. Go to your Home Assistant dashboard
# 3. Click "Edit Dashboard" → "Add Card" → "Manual"
# 4. Paste the YAML configuration
# 5. Update entity names to match your setup
# 6. Save the card

# Note: Card 3 requires the Mushroom Cards custom component
# Install via HACS: https://github.com/piitaya/lovelace-mushroom
