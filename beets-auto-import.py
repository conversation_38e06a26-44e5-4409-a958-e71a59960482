#!/usr/bin/env python3
"""
Beets Auto-Import Service
Watches for new music files and automatically imports them using beets.
"""

import os
import sys
import time
import subprocess
import logging
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

# Configuration
WATCH_DIRECTORY = "/mnt/local/yams/slskd/complete"  # Directory to watch for new music
BEETS_CONFIG = "/home/<USER>/.config/beets/config.yaml"  # Path to your beets config
BEETS_COMMAND = "uv run beet"  # Command to run beets (adjust for your setup)
WORKING_DIR = "/opt/beetsd"  # Working directory for beets commands
LOG_FILE = "/var/log/beets-auto-import.log"
DEBOUNCE_SECONDS = 30  # Wait time after file changes before importing
AUDIO_EXTENSIONS = {'.mp3', '.flac', '.ogg', '.m4a', '.aac', '.wma', '.wav'}

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class MusicImportHandler(FileSystemEventHandler):
    def __init__(self):
        self.pending_imports = {}  # Track directories with pending imports
        
    def on_created(self, event):
        if event.is_directory:
            logger.info(f"New directory created: {event.src_path}")
            self.schedule_import(event.src_path)
        elif self.is_audio_file(event.src_path):
            logger.info(f"New audio file: {event.src_path}")
            # Schedule import for the parent directory
            parent_dir = os.path.dirname(event.src_path)
            self.schedule_import(parent_dir)
    
    def on_moved(self, event):
        if event.is_directory:
            logger.info(f"Directory moved to: {event.dest_path}")
            self.schedule_import(event.dest_path)
        elif self.is_audio_file(event.dest_path):
            logger.info(f"Audio file moved to: {event.dest_path}")
            parent_dir = os.path.dirname(event.dest_path)
            self.schedule_import(parent_dir)
    
    def is_audio_file(self, filepath):
        """Check if file is an audio file we care about."""
        return Path(filepath).suffix.lower() in AUDIO_EXTENSIONS
    
    def schedule_import(self, directory):
        """Schedule an import for a directory after debounce period."""
        # Cancel any existing timer for this directory
        if directory in self.pending_imports:
            self.pending_imports[directory].cancel()
        
        # Schedule new import
        import threading
        timer = threading.Timer(DEBOUNCE_SECONDS, self.import_directory, [directory])
        self.pending_imports[directory] = timer
        timer.start()
        logger.info(f"Scheduled import for {directory} in {DEBOUNCE_SECONDS} seconds")
    
    def import_directory(self, directory):
        """Import a directory using beets."""
        try:
            # Remove from pending imports
            if directory in self.pending_imports:
                del self.pending_imports[directory]
            
            # Check if directory still exists and has audio files
            if not os.path.exists(directory):
                logger.warning(f"Directory no longer exists: {directory}")
                return
            
            if not self.has_audio_files(directory):
                logger.info(f"No audio files found in: {directory}")
                return
            
            logger.info(f"Starting import of: {directory}")
            
            # Build beets import command
            cmd = [
                "bash", "-c", 
                f"cd {WORKING_DIR} && {BEETS_COMMAND} import --quiet '{directory}'"
            ]
            
            # Run the import
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            if result.returncode == 0:
                logger.info(f"Successfully imported: {directory}")
                if result.stdout:
                    logger.debug(f"Import output: {result.stdout}")
            else:
                logger.error(f"Import failed for {directory}: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            logger.error(f"Import timeout for: {directory}")
        except Exception as e:
            logger.error(f"Error importing {directory}: {e}")
    
    def has_audio_files(self, directory):
        """Check if directory contains audio files."""
        try:
            for root, dirs, files in os.walk(directory):
                for file in files:
                    if self.is_audio_file(file):
                        return True
            return False
        except Exception as e:
            logger.error(f"Error checking directory {directory}: {e}")
            return False

def main():
    """Main function to start the file watcher."""
    logger.info("Starting Beets Auto-Import Service")
    logger.info(f"Watching directory: {WATCH_DIRECTORY}")
    logger.info(f"Using beets config: {BEETS_CONFIG}")
    
    # Verify watch directory exists
    if not os.path.exists(WATCH_DIRECTORY):
        logger.error(f"Watch directory does not exist: {WATCH_DIRECTORY}")
        sys.exit(1)
    
    # Verify beets is available
    try:
        result = subprocess.run(
            ["bash", "-c", f"cd {WORKING_DIR} && {BEETS_COMMAND} version"],
            capture_output=True,
            text=True,
            timeout=10
        )
        if result.returncode != 0:
            logger.error("Beets command failed. Check your configuration.")
            sys.exit(1)
        logger.info(f"Beets version: {result.stdout.strip()}")
    except Exception as e:
        logger.error(f"Error checking beets: {e}")
        sys.exit(1)
    
    # Set up file system watcher
    event_handler = MusicImportHandler()
    observer = Observer()
    observer.schedule(event_handler, WATCH_DIRECTORY, recursive=True)
    
    try:
        observer.start()
        logger.info("File watcher started successfully")
        
        # Keep the script running
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("Received interrupt signal, shutting down...")
        observer.stop()
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        observer.stop()
        sys.exit(1)
    
    observer.join()
    logger.info("Beets Auto-Import Service stopped")

if __name__ == "__main__":
    main()
