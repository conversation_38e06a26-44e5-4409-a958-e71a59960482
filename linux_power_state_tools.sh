#!/bin/bash

# Linux Tools for Adding Missing AMD GPU Power States
# Alternative to VBIOS flashing

echo "=== Linux Power State Management Tools ==="
echo ""

# Check current state
echo "Current VRAM states:"
cat /sys/class/drm/card0/device/pp_dpm_mclk
echo ""

echo "=== Method 1: Advanced OverDrive Manipulation ==="
echo ""

# Try to add custom memory states via OverDrive
echo "Attempting to add custom memory states..."

# First, enable manual mode
sudo sh -c 'echo "manual" > /sys/class/drm/card0/device/power_dpm_force_performance_level'

# Try to add new memory states beyond the current maximum
echo "Trying to add state 4 (1500MHz)..."
sudo sh -c 'echo "m 4 1500" > /sys/class/drm/card0/device/pp_od_clk_voltage' 2>/dev/null
sudo sh -c 'echo "c" > /sys/class/drm/card0/device/pp_od_clk_voltage' 2>/dev/null

echo "Trying to add state 5 (1750MHz)..."
sudo sh -c 'echo "m 5 1750" > /sys/class/drm/card0/device/pp_od_clk_voltage' 2>/dev/null
sudo sh -c 'echo "c" > /sys/class/drm/card0/device/pp_od_clk_voltage' 2>/dev/null

echo "Trying to add state 6 (2000MHz)..."
sudo sh -c 'echo "m 6 2000" > /sys/class/drm/card0/device/pp_od_clk_voltage' 2>/dev/null
sudo sh -c 'echo "c" > /sys/class/drm/card0/device/pp_od_clk_voltage' 2>/dev/null

echo "States after OverDrive manipulation:"
cat /sys/class/drm/card0/device/pp_dpm_mclk
echo ""

echo "=== Method 2: Power Profile Switching ==="
echo ""

# Try different power profiles that might unlock states
PROFILES=("0" "1" "2" "3" "4" "5" "6")
PROFILE_NAMES=("BOOTUP_DEFAULT" "3D_FULL_SCREEN" "POWER_SAVING" "VIDEO" "VR" "COMPUTE" "CUSTOM")

for i in "${!PROFILES[@]}"; do
    profile="${PROFILES[$i]}"
    name="${PROFILE_NAMES[$i]}"
    
    echo "Testing profile $profile ($name)..."
    sudo sh -c "echo '$profile' > /sys/class/drm/card0/device/pp_power_profile_mode" 2>/dev/null
    
    # Check if new states appeared
    STATES=$(cat /sys/class/drm/card0/device/pp_dpm_mclk | wc -l)
    MAX_FREQ=$(cat /sys/class/drm/card0/device/pp_dpm_mclk | tail -1 | awk '{print $2}' | sed 's/Mhz//')
    
    echo "  States: $STATES, Max frequency: ${MAX_FREQ}MHz"
    
    if [ "$STATES" -gt 4 ] || [ "$MAX_FREQ" -gt 1100 ]; then
        echo "  ✅ Profile $name shows improvement!"
        cat /sys/class/drm/card0/device/pp_dpm_mclk
        echo ""
    fi
done

echo ""
echo "=== Method 3: Advanced Kernel Parameters ==="
echo ""

# Check current feature mask
echo "Current ppfeaturemask: $(cat /sys/module/amdgpu/parameters/ppfeaturemask 2>/dev/null || echo 'Not accessible')"

# Try to modify feature mask at runtime (usually requires reboot)
echo "Attempting runtime feature mask changes..."

# Try different feature masks
FEATURE_MASKS=("0xffffffff" "0x4fff7fff" "0xfffffffe" "0xfff7ffff")

for mask in "${FEATURE_MASKS[@]}"; do
    echo "Testing feature mask: $mask"
    
    # Try to set it (may not work without reboot)
    if sudo sh -c "echo '$mask' > /sys/module/amdgpu/parameters/ppfeaturemask" 2>/dev/null; then
        echo "  ✅ Feature mask set successfully"
        
        # Check if it unlocked new states
        sleep 1
        STATES=$(cat /sys/class/drm/card0/device/pp_dpm_mclk | wc -l)
        if [ "$STATES" -gt 4 ]; then
            echo "  🎉 New memory states detected!"
            cat /sys/class/drm/card0/device/pp_dpm_mclk
        fi
    else
        echo "  ❌ Cannot set feature mask at runtime"
    fi
done

echo ""
echo "=== Method 4: Direct sysfs Manipulation ==="
echo ""

# Try to directly write to DPM files
echo "Attempting direct DPM manipulation..."

# Try to force enable additional states
for state in 4 5 6 7; do
    echo "Trying to enable state $state..."
    sudo sh -c "echo '$state' > /sys/class/drm/card0/device/pp_dpm_mclk" 2>/dev/null
    
    if [ $? -eq 0 ]; then
        echo "  ✅ State $state enabled"
        cat /sys/class/drm/card0/device/pp_dpm_mclk | tail -3
    else
        echo "  ❌ Cannot enable state $state"
    fi
done

echo ""
echo "=== Method 5: Performance Level Manipulation ==="
echo ""

# Try different performance levels
PERF_LEVELS=("auto" "low" "high" "manual" "profile_standard" "profile_min_sclk" "profile_min_mclk" "profile_peak")

for level in "${PERF_LEVELS[@]}"; do
    echo "Testing performance level: $level"
    
    if sudo sh -c "echo '$level' > /sys/class/drm/card0/device/power_dpm_force_performance_level" 2>/dev/null; then
        echo "  ✅ Level set successfully"
        
        # Check memory states
        STATES=$(cat /sys/class/drm/card0/device/pp_dpm_mclk | wc -l)
        MAX_FREQ=$(cat /sys/class/drm/card0/device/pp_dpm_mclk | tail -1 | awk '{print $2}' | sed 's/Mhz//')
        
        if [ "$STATES" -gt 4 ] || [ "$MAX_FREQ" -gt 1100 ]; then
            echo "  🎉 Performance level $level shows improvement!"
            cat /sys/class/drm/card0/device/pp_dpm_mclk
            echo ""
        fi
    else
        echo "  ❌ Cannot set performance level $level"
    fi
done

echo ""
echo "=== Method 6: Third-Party Tools ==="
echo ""

# Check for available tools
echo "Checking for third-party GPU management tools..."

# CoreCtrl
if command -v corectrl &> /dev/null; then
    echo "✅ CoreCtrl found - GUI tool for GPU management"
    echo "  Try: corectrl (may have advanced memory controls)"
else
    echo "❌ CoreCtrl not installed"
    echo "  Install with: sudo pacman -S corectrl"
fi

# AMDGPU-Utils
if command -v amdgpu-monitor &> /dev/null; then
    echo "✅ amdgpu-utils found"
    echo "  Try: amdgpu-monitor --gui"
else
    echo "❌ amdgpu-utils not installed"
    echo "  Install with: yay -S amdgpu-utils"
fi

# RadeonTop
if command -v radeontop &> /dev/null; then
    echo "✅ RadeonTop found"
    echo "  Monitor with: radeontop"
else
    echo "❌ RadeonTop not installed"
    echo "  Install with: sudo pacman -S radeontop"
fi

# WattmanGTK
if command -v wattman-gtk &> /dev/null; then
    echo "✅ WattmanGTK found - Advanced GPU control"
    echo "  Try: wattman-gtk (may allow custom memory states)"
else
    echo "❌ WattmanGTK not installed"
    echo "  Install with: yay -S wattman-gtk"
fi

echo ""
echo "=== Final Status Check ==="
echo ""

echo "Final VRAM states:"
cat /sys/class/drm/card0/device/pp_dpm_mclk

FINAL_STATES=$(cat /sys/class/drm/card0/device/pp_dpm_mclk | wc -l)
FINAL_MAX=$(cat /sys/class/drm/card0/device/pp_dpm_mclk | tail -1 | awk '{print $2}' | sed 's/Mhz//')

echo ""
echo "Results:"
echo "Memory states: $FINAL_STATES"
echo "Maximum frequency: ${FINAL_MAX}MHz"

if [ "$FINAL_STATES" -gt 4 ] || [ "$FINAL_MAX" -gt 1100 ]; then
    echo "🎉 SUCCESS: Improvements detected!"
    echo "Some methods may have unlocked additional power states."
else
    echo "❌ LIMITED SUCCESS: Still restricted by VBIOS"
    echo ""
    echo "Unfortunately, your mining VBIOS has hard limits that cannot be"
    echo "bypassed through software alone. Consider:"
    echo "1. VBIOS modification/flashing"
    echo "2. Contacting Sapphire for proper gaming VBIOS"
    echo "3. Using Windows-based tools (MorePowerTool)"
fi

echo ""
echo "=== Tool Installation Commands ==="
echo ""
echo "To install additional GPU management tools:"
echo "sudo pacman -S corectrl radeontop"
echo "yay -S amdgpu-utils wattman-gtk"
