# Sonos Sub Dynamic Group Management Setup Guide

## Overview
This automation system dynamically moves your Sonos Sub between two speaker groups based on playback activity:
- **Default**: Sub stays with the 5.1 surround sound system
- **Dynamic**: Sub moves to stereo pair when stereo starts playing
- **Priority**: Surround system takes priority when it starts playing

## Technical Feasibility ✅
**CONFIRMED**: This automation is technically possible using Home Assistant's Sonos integration with the `media_player.join` and `media_player.unjoin` services.

## Prerequisites

### 1. Home Assistant Sonos Integration
Ensure the Sonos integration is properly configured:
```yaml
# Usually auto-discovered, but manual config if needed:
sonos:
  media_player:
    hosts:
      - *************  # Replace with your Sonos speaker IPs
      - *************
      - *************
```

### 2. Network Requirements
- Sonos devices must reach Home Assistant on TCP port 1400
- For Docker deployments, ensure proper network configuration
- Consider using `--net=host` for Docker if experiencing connectivity issues

### 3. Entity Identification
Find your actual Sonos entity names in Home Assistant:
1. Go to **Settings** > **Devices & Services** > **Sonos**
2. Note down the entity IDs for:
   - Main surround speaker (coordinator)
   - Sonos Sub
   - Stereo pair speakers (left and right)

## Installation Steps

### Step 1: Update Entity Names
Edit `sonos_sub_automation.yaml` and replace these placeholder entity names with your actual ones:
```yaml
# REPLACE THESE WITH YOUR ACTUAL ENTITY NAMES:
media_player.sonos_surround_main    # Your 5.1 main speaker
media_player.sonos_sub              # Your Sonos Sub
media_player.sonos_stereo_left      # Left speaker of stereo pair
media_player.sonos_stereo_right     # Right speaker of stereo pair
```

### Step 2: Add Automation to Home Assistant

#### Option A: Using automations.yaml
If you use a single `automations.yaml` file, copy the automation sections from `sonos_sub_automation.yaml` into your existing file.

#### Option B: Using packages (Recommended)
1. Create a packages directory if it doesn't exist:
   ```yaml
   # In configuration.yaml
   homeassistant:
     packages: !include_dir_named packages
   ```

2. Place `sonos_sub_automation.yaml` in the `packages/` directory

#### Option C: Using the UI
1. Go to **Settings** > **Automations & Scenes**
2. Create new automations using the YAML from the file

### Step 3: Test the Setup

#### Initial Testing
1. **Test Manual Scripts First**:
   - Run `sonos_sub_to_stereo_manual` script
   - Verify Sub joins stereo group
   - Run `sonos_sub_to_surround_manual` script
   - Verify Sub returns to surround group

2. **Test Automatic Triggers**:
   - Start playing music on stereo pair
   - Wait 2 seconds, verify Sub joins stereo group
   - Stop stereo music, wait 30 seconds
   - Verify Sub returns to surround group

## Automation Logic

### Automation 1: Move Sub to Stereo
**Trigger**: Stereo speakers start playing
**Conditions**:
- Stereo group is actually playing
- Sub is not already in stereo group
- Surround system is not actively playing
**Action**: Unjoin Sub, then join to stereo group

### Automation 2: Return Sub to Surround
**Trigger**: Stereo speakers stop playing (30-second delay)
**Conditions**:
- Both stereo speakers are not playing
- Sub is currently in stereo group
**Action**: Unjoin Sub, then join to surround group

### Automation 3: Surround Priority Override
**Trigger**: Surround system starts playing
**Conditions**:
- Sub is not already in surround group
**Action**: Immediately move Sub to surround group

## Edge Case Handling

### Multiple Music Sources
- **Priority System**: Surround system takes priority over stereo
- **Delay Logic**: 30-second delay before returning Sub to prevent rapid switching
- **State Verification**: Checks actual playback state, not just entity state changes

### Overlapping Playback
- Surround system automatically reclaims Sub when it starts playing
- Prevents conflicts between simultaneous playback on both systems

### Network Issues
- Single mode prevents multiple automation instances
- Delay between unjoin and join operations for stability

## Troubleshooting

### Common Issues

1. **Sub Not Moving Between Groups**
   - Check entity names are correct
   - Verify Sonos integration is working
   - Check Home Assistant logs for errors

2. **Rapid Switching**
   - Increase delay timers if needed
   - Check for conflicting automations

3. **Docker Network Issues**
   - Ensure Sonos can reach Home Assistant on port 1400
   - Consider using `--net=host` for Docker

### Debug Commands
```yaml
# Check current group membership
{{ state_attr('media_player.sonos_sub', 'group_members') }}

# Check speaker states
{{ states('media_player.sonos_stereo_left') }}
{{ states('media_player.sonos_surround_main') }}
```

## Optional Enhancements

### Dashboard Controls
Add manual control buttons to your dashboard:
```yaml
# In your dashboard YAML
type: entities
entities:
  - entity: script.sonos_sub_to_stereo_manual
    name: "Move Sub to Stereo"
  - entity: script.sonos_sub_to_surround_manual
    name: "Move Sub to Surround"
```

### Notifications
Add notifications when Sub moves:
```yaml
# Add to automation actions
- service: notify.mobile_app_your_phone
  data:
    message: "Sonos Sub moved to {{ 'stereo' if 'stereo' in trigger.entity_id else 'surround' }} group"
```

### Advanced Conditions
Consider adding time-based conditions:
```yaml
# Only during certain hours
- condition: time
  after: "08:00:00"
  before: "22:00:00"
```

## Security Considerations
- This automation only uses standard Home Assistant media player services
- No external network access required
- All operations are local to your network

## Performance Impact
- Minimal CPU usage
- Network traffic only during group changes
- No continuous polling required

## Support
If you encounter issues:
1. Check Home Assistant logs
2. Verify Sonos integration status
3. Test manual scripts first
4. Consider posting in Home Assistant Community forums with specific error messages
