# VBIOS Solution Guide for RX 6800 XT VRAM Issue

## Problem Confirmed
Your Sapphire RX 6800 XT Nitro+ has a limited VBIOS that caps VRAM at ~1075MHz instead of 2000MHz.

## Solution 1: MorePowerTool (Recommended - Safest)

### Requirements
- Windows (dual boot or VM)
- MorePowerTool from <PERSON>'s Lab
- GPU-Z for VBIOS backup

### Steps
1. **Boot to Windows**
2. **Download tools:**
   - MorePowerTool: https://www.igorslab.de/morepowertool/
   - GPU-Z: https://www.techpowerup.com/gpuz/
   - ATIFlash: https://www.techpowerup.com/download/ati-atiflash/

3. **Backup current VBIOS:**
   ```cmd
   # In GPU-Z, click the BIOS button and save as backup.rom
   ```

4. **Modify VBIOS in MorePowerTool:**
   - Load your backup.rom
   - Go to "Memory" tab
   - Add memory states:
     * State 4: 1500MHz
     * State 5: 1750MHz  
     * State 6: 2000MHz
   - Adjust memory timings if needed
   - Save as modified.rom

5. **Flash modified VBIOS:**
   ```cmd
   atiflash -p 0 modified.rom
   ```

6. **Reboot and test in Linux**

## Solution 2: Download Proper VBIOS

### Find correct VBIOS
1. Go to TechPowerUp VBIOS database
2. Search for "Sapphire RX 6800 XT Nitro+"
3. Download a VBIOS with proper memory support
4. Flash using ATIFlash

### Recommended VBIOS versions
- Look for VBIOS with "2000MHz" memory support
- Avoid "OEM" or "Limited" versions
- Check user comments for Linux compatibility

## Solution 3: Linux VBIOS Flashing (Advanced)

### Using amdvbflash (if available)
```bash
# Install amdvbflash
yay -S amdvbflash

# Backup current VBIOS
sudo amdvbflash -s 0 backup.rom

# Flash new VBIOS
sudo amdvbflash -p 0 new_vbios.rom
```

## Solution 4: Contact Sapphire Support

### What to tell them
- Model: RX 6800 XT Nitro+
- Issue: VRAM limited to 1000MHz instead of 2000MHz
- Request: Updated VBIOS with proper memory support
- Mention: Linux compatibility needed

## Temporary Workaround Script

While you work on VBIOS solution, use this script to maximize current performance:
```bash
#!/bin/bash
# Maximize performance with current limited VBIOS

# Set manual mode
sudo sh -c 'echo "manual" > /sys/class/drm/card0/device/power_dpm_force_performance_level'

# Overclock to maximum allowed
sudo sh -c 'echo "m 1 1075" > /sys/class/drm/card0/device/pp_od_clk_voltage'
sudo sh -c 'echo "c" > /sys/class/drm/card0/device/pp_od_clk_voltage'

# Force highest memory state
sudo sh -c 'echo "3" > /sys/class/drm/card0/device/pp_dpm_mclk'

echo "VRAM overclocked to maximum: 1075MHz"
```

## Risk Assessment

### Low Risk
- MorePowerTool modification
- Official VBIOS from TechPowerUp
- Contacting Sapphire support

### Medium Risk  
- Flashing third-party VBIOS
- Linux-based flashing tools

### High Risk
- Modifying VBIOS without backup
- Using incompatible VBIOS files

## Recovery Plan

If VBIOS flash fails:
1. **Dual BIOS switch** (if your card has one)
2. **Recovery flash:**
   ```cmd
   atiflash -f -p 0 backup.rom
   ```
3. **Hardware recovery** (shorting pins - last resort)

## Expected Results

After proper VBIOS:
- VRAM states: 96MHz → 456MHz → 673MHz → 1000MHz → 1500MHz → 1750MHz → 2000MHz
- Significant performance improvement in memory-intensive tasks
- Proper frequency scaling under load

## Alternative: New GPU

If VBIOS modification seems too risky:
- Consider RMA if under warranty
- Upgrade to newer GPU with proper VBIOS
- Buy from different manufacturer

## Community Resources

- r/AMD subreddit
- Igor's Lab forums  
- TechPowerUp forums
- AMD GPU Discord servers
