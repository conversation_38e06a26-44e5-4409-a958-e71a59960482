[Unit]
Description=Beets Auto-Import Service
Documentation=https://beets.readthedocs.io/
After=network.target
Wants=network.target

[Service]
Type=simple
User=erik
Group=erik
WorkingDirectory=/opt/beetsd
Environment=PATH=/usr/local/bin:/usr/bin:/bin
Environment=PYTHONPATH=/opt/beetsd/.venv/lib/python3.11/site-packages
ExecStart=/usr/bin/python3 /home/<USER>/scripts/beets-auto-import.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=false
ReadWritePaths=/mnt/data/media/music /opt/beets /var/log /mnt/local/yams/slskd/complete

[Install]
WantedBy=multi-user.target
