#!/bin/bash

# Flash 267602.rom VBIOS
set -e

VBIOS_FILE="267602.rom"
BACKUP_FILE="backup_before_267602_$(date +%Y%m%d_%H%M%S).rom"

echo "=== Flashing 267602.rom VBIOS ==="
echo ""

# Pre-flight checks
if [ "$EUID" -ne 0 ]; then
    echo "❌ This script must be run as root (sudo)"
    exit 1
fi

if [ ! -f "$VBIOS_FILE" ]; then
    echo "❌ VBIOS file '$VBIOS_FILE' not found"
    exit 1
fi

echo "✅ VBIOS file found: $VBIOS_FILE"
echo "✅ File size: $(stat -c%s "$VBIOS_FILE") bytes"

# Show current GPU status
echo ""
echo "Current GPU Status:"
amdvbflash -i

# Create backup
echo ""
echo "Creating backup: $BACKUP_FILE"
amdvbflash -s 0 "$BACKUP_FILE"

if [ ! -f "$BACKUP_FILE" ]; then
    echo "❌ Failed to create backup!"
    exit 1
fi

echo "✅ Backup created: $BACKUP_FILE"

# Show version comparison
echo ""
echo "Version Comparison:"
echo "Current VBIOS: 113-438MI-U89 (Mining BIOS)"
echo "New VBIOS:     113-438XTMI-U76 (Gaming/Mining hybrid?)"
echo ""

# Final confirmation
echo "⚠️  FINAL WARNING: About to flash GPU firmware!"
echo "Type 'FLASH267602' to proceed:"
read -r confirmation

if [ "$confirmation" != "FLASH267602" ]; then
    echo "❌ Aborted by user"
    exit 1
fi

# Attempt flash
echo ""
echo "🔥 FLASHING VBIOS - DO NOT INTERRUPT!"
echo ""

# Try multiple methods
echo "Method 1: Standard flash..."
if amdvbflash -p 0 "$VBIOS_FILE"; then
    echo "✅ Flash successful with Method 1!"
else
    echo "Method 1 failed, trying Method 2..."
    
    # Try with adapter specification
    if amdvbflash -p 0 -a 0 "$VBIOS_FILE"; then
        echo "✅ Flash successful with Method 2!"
    else
        echo "Method 2 failed, trying Method 3..."
        
        # Try different approach
        if amdvbflash -fa -p 0 "$VBIOS_FILE" 2>/dev/null; then
            echo "✅ Flash successful with Method 3!"
        else
            echo "❌ All flash methods failed"
            echo ""
            echo "Error likely due to version protection in external amdvbflash"
            echo "Recommendations:"
            echo "1. Try flashing from Windows with ATIFlash"
            echo "2. Look for newer VBIOS version (>U89)"
            echo "3. Check for hardware BIOS switch on GPU"
            exit 1
        fi
    fi
fi

# Verify flash
echo ""
echo "Verifying flash..."
amdvbflash -i

echo ""
echo "🎉 VBIOS Flash Complete!"
echo ""
echo "CRITICAL: REBOOT REQUIRED!"
echo "After reboot, check VRAM states with:"
echo "  cat /sys/class/drm/card0/device/pp_dpm_mclk"
echo ""
echo "Expected result: More than 4 memory states with higher frequencies"
echo "Backup saved as: $BACKUP_FILE"
