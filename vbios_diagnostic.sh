#!/bin/bash

# Comprehensive VBIOS and Power Table Diagnostic
echo "=== RX 6800 XT VBIOS Diagnostic ==="
echo "This will help identify why your GPU has limited VRAM frequency states"
echo ""

# 1. Check VBIOS version and date
echo "1. VBIOS Information:"
if [ -r /sys/class/drm/card0/device/vbios_version ]; then
    echo "VBIOS Version: $(cat /sys/class/drm/card0/device/vbios_version)"
else
    echo "VBIOS version not accessible"
fi

# 2. Check GPU identification
echo ""
echo "2. GPU Identification:"
lspci -nn | grep VGA
echo "Device ID: $(cat /sys/class/drm/card0/device/device)"
echo "Vendor ID: $(cat /sys/class/drm/card0/device/vendor)"
echo "Subsystem Device: $(cat /sys/class/drm/card0/device/subsystem_device)"
echo "Subsystem Vendor: $(cat /sys/class/drm/card0/device/subsystem_vendor)"

# 3. Check power table size and accessibility
echo ""
echo "3. Power Table Analysis:"
if [ -r /sys/class/drm/card0/device/pp_table ]; then
    PP_SIZE=$(wc -c < /sys/class/drm/card0/device/pp_table)
    echo "Power table size: $PP_SIZE bytes"
    
    # Check if it's a valid size (should be several KB for full table)
    if [ "$PP_SIZE" -lt 1000 ]; then
        echo "⚠ WARNING: Power table seems too small ($PP_SIZE bytes)"
        echo "This indicates a limited or corrupted power table"
    elif [ "$PP_SIZE" -gt 10000 ]; then
        echo "✓ Power table size looks normal"
    else
        echo "? Power table size is moderate - may be limited"
    fi
else
    echo "✗ Power table not accessible"
fi

# 4. Check memory configuration
echo ""
echo "4. Memory Configuration:"
echo "Total VRAM: $(($(cat /sys/class/drm/card0/device/mem_info_vram_total)/1024/1024/1024))GB"
echo "Memory Type: GDDR6 (expected for RX 6800 XT)"

# 5. Check if this is a known issue with specific VBIOS versions
echo ""
echo "5. Known Issues Check:"
DEVICE_ID=$(cat /sys/class/drm/card0/device/device)
SUBSYS_DEVICE=$(cat /sys/class/drm/card0/device/subsystem_device)

echo "Device ID: $DEVICE_ID"
echo "Subsystem: $SUBSYS_DEVICE"

# Sapphire RX 6800 XT Nitro+ specific checks
if [ "$DEVICE_ID" = "0x73bf" ]; then
    echo "✓ Confirmed: AMD Navi 21 (RX 6800 XT)"
    
    case "$SUBSYS_DEVICE" in
        "0x1e3b"|"0x1e3c"|"0x1e3d")
            echo "⚠ KNOWN ISSUE: Some Sapphire Nitro+ cards have limited VBIOS"
            echo "This is a common issue with certain VBIOS versions"
            ;;
        *)
            echo "? Unknown Sapphire variant: $SUBSYS_DEVICE"
            ;;
    esac
else
    echo "? Unexpected device ID: $DEVICE_ID"
fi

# 6. Check current driver capabilities
echo ""
echo "6. Driver Capabilities:"
echo "AMDGPU driver version: $(modinfo amdgpu | grep '^version:' | awk '{print $2}')"
echo "Mesa version: $(pacman -Q mesa | awk '{print $2}')"

# 7. Check if OverDrive is supported
echo ""
echo "7. OverDrive Support:"
if [ -r /sys/class/drm/card0/device/pp_od_clk_voltage ]; then
    echo "✓ OverDrive is available"
    echo "OverDrive capabilities:"
    cat /sys/class/drm/card0/device/pp_od_clk_voltage | head -10
else
    echo "✗ OverDrive not available - this limits memory overclocking"
fi

# 8. Recommendations based on findings
echo ""
echo "=== DIAGNOSTIC RESULTS & RECOMMENDATIONS ==="
echo ""

if [ "$PP_SIZE" -lt 1000 ]; then
    echo "🔴 CRITICAL: Your power table is severely limited"
    echo "SOLUTION: VBIOS modification required"
    echo ""
    echo "Recommended actions:"
    echo "1. Boot to Windows and use MorePowerTool to modify VBIOS"
    echo "2. Download a proper VBIOS from TechPowerUp database"
    echo "3. Contact Sapphire support for VBIOS update"
    
elif [ ! -r /sys/class/drm/card0/device/pp_od_clk_voltage ]; then
    echo "🟡 MODERATE: OverDrive disabled, limiting memory control"
    echo "SOLUTION: Try different kernel parameters or VBIOS update"
    echo ""
    echo "Try these kernel parameters (one at a time, reboot between tests):"
    echo "- amdgpu.ppfeaturemask=0xfff7ffff (already set)"
    echo "- amdgpu.ppfeaturemask=0x4fff7fff"
    echo "- amdgpu.ppfeaturemask=0xfffffffe"
    
else
    echo "🟢 GOOD: Hardware and drivers look normal"
    echo "SOLUTION: The issue is likely VBIOS-specific"
    echo ""
    echo "Try manual memory overclocking:"
    echo "sudo sh -c 'echo \"m 3 1200\" > /sys/class/drm/card0/device/pp_od_clk_voltage'"
fi

echo ""
echo "=== IMMEDIATE NEXT STEPS ==="
echo "1. Reboot with the new kernel parameter (0xfff7ffff)"
echo "2. If that doesn't work, try VBIOS modification"
echo "3. Consider updating to latest VBIOS from Sapphire"
echo "4. Join r/Amd or AMD GPU communities for VBIOS files"
