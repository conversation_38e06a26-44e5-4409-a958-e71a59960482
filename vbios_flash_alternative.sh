#!/bin/bash

# Alternative VBIOS Flash Method
# Using different approach for external amdvbflash

VBIOS_FILE="Sapphire.RX6800XT.16384.210112.rom"
BACKUP_FILE="original_vbios_backup_20250726_135327.rom"

echo "=== Alternative VBIOS Flash Method ==="
echo ""

# Check if we have the backup
if [ ! -f "$BACKUP_FILE" ]; then
    echo "❌ Backup file not found! Creating new backup..."
    sudo amdvbflash -s 0 "$BACKUP_FILE"
fi

echo "✅ Backup available: $BACKUP_FILE"
echo ""

# Try different flash approaches
echo "Method 1: Try without force flag..."
sudo amdvbflash -p 0 "$VBIOS_FILE"

if [ $? -ne 0 ]; then
    echo ""
    echo "Method 1 failed. Trying Method 2..."
    echo ""
    
    # Method 2: Try with adapter specification
    echo "Method 2: Specifying adapter explicitly..."
    sudo amdvbflash -p 0 -a 0 "$VBIOS_FILE"
    
    if [ $? -ne 0 ]; then
        echo ""
        echo "Method 2 failed. Trying Method 3..."
        echo ""
        
        # Method 3: Check if we need to use different tool
        echo "Method 3: Checking alternative tools..."
        
        # Check if atiflash is available
        if command -v atiflash &> /dev/null; then
            echo "Found atiflash, trying with it..."
            sudo atiflash -f -p 0 "$VBIOS_FILE"
        else
            echo "atiflash not available"
        fi
        
        # Check if we can use the internal version
        if [ -f "/usr/bin/amdvbflash-internal" ]; then
            echo "Trying internal version..."
            sudo amdvbflash-internal -f -p 0 "$VBIOS_FILE"
        fi
    fi
fi

echo ""
echo "=== Flash Status Check ==="
sudo amdvbflash -i

echo ""
echo "If flash was successful, you should see a different BIOS P/N"
echo "Original: 113-438MI-U89"
echo "Expected after flash: Different P/N"
