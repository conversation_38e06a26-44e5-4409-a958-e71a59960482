#!/bin/bash
# Setup script for Beets Auto-Import Service

set -e

echo "Setting up Beets Auto-Import Service..."

# Install required Python package
echo "Installing watchdog package..."
cd /opt/beetsd
uv add watchdog

# Make the Python script executable
chmod +x /home/<USER>/scripts/beets-auto-import.py

# Create log directory if it doesn't exist
sudo mkdir -p /var/log
sudo touch /var/log/beets-auto-import.log
sudo chown erik:erik /var/log/beets-auto-import.log

# Copy systemd service file
echo "Installing systemd service..."
sudo cp /home/<USER>/scripts/beets-auto-import.service /etc/systemd/system/

# Reload systemd and enable the service
sudo systemctl daemon-reload
sudo systemctl enable beets-auto-import.service

echo "Setup complete!"
echo ""
echo "To start the service:"
echo "  sudo systemctl start beets-auto-import"
echo ""
echo "To check status:"
echo "  sudo systemctl status beets-auto-import"
echo ""
echo "To view logs:"
echo "  sudo journalctl -u beets-auto-import -f"
echo "  tail -f /var/log/beets-auto-import.log"
echo ""
echo "To stop the service:"
echo "  sudo systemctl stop beets-auto-import"
