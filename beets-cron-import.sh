#!/bin/bash
# Simple cron-based beets import script
# Add to crontab with: */5 * * * * /home/<USER>/scripts/beets-cron-import.sh

# Configuration
WATCH_DIR="/mnt/local/yams/slskd/complete"
BEETS_DIR="/opt/beetsd"
LOG_FILE="/var/log/beets-cron-import.log"
LOCK_FILE="/tmp/beets-import.lock"
PROCESSED_FILE="/tmp/beets-processed-dirs.txt"

# Function to log messages
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

# Check if another instance is running
if [ -f "$LOCK_FILE" ]; then
    log_message "Another import process is running, exiting"
    exit 0
fi

# Create lock file
echo $$ > "$LOCK_FILE"

# Cleanup function
cleanup() {
    rm -f "$LOCK_FILE"
}
trap cleanup EXIT

# Create processed file if it doesn't exist
touch "$PROCESSED_FILE"

log_message "Starting beets import scan"

# Find new directories with audio files
find "$WATCH_DIR" -type d -name "*" | while read -r dir; do
    # Skip if directory is empty or doesn't contain audio files
    if ! find "$dir" -maxdepth 1 -type f \( -iname "*.mp3" -o -iname "*.flac" -o -iname "*.ogg" -o -iname "*.m4a" -o -iname "*.aac" \) | grep -q .; then
        continue
    fi
    
    # Skip if already processed
    if grep -Fxq "$dir" "$PROCESSED_FILE"; then
        continue
    fi
    
    # Check if directory is stable (no recent changes)
    if [ $(find "$dir" -type f -mmin -5 | wc -l) -gt 0 ]; then
        log_message "Directory $dir has recent changes, skipping"
        continue
    fi
    
    log_message "Importing new directory: $dir"
    
    # Change to beets directory and run import
    cd "$BEETS_DIR"
    if uv run beet import --quiet "$dir" >> "$LOG_FILE" 2>&1; then
        log_message "Successfully imported: $dir"
        echo "$dir" >> "$PROCESSED_FILE"
    else
        log_message "Failed to import: $dir"
    fi
done

log_message "Import scan completed"
